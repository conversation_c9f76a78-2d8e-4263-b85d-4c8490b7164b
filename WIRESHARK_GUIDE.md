# Wireshark抓包和解密HTTP/3流量指南

## 概述

本指南将教您如何使用Wireshark抓包并解密HTTP/3 QPACK流量，以便深入分析QPACK动态表的工作原理。

## 准备工作

### 1. 安装Wireshark
- 下载并安装最新版本的Wireshark (支持HTTP/3的版本 >= 3.4)
- 确保安装了QUIC/HTTP3解析器

### 2. 生成SSL密钥日志
使用我们的客户端生成SSL密钥日志文件：

```bash
# 详细调试版本
python qpack_debug_client.py --host aliyun.hawks.top --requests 5 --insecure --secrets-log ssl_keys.log

# 演示版本
python qpack_demo.py --secrets-log ssl_keys.log
```

## Wireshark配置步骤

### 1. 配置TLS密钥日志
1. 打开Wireshark
2. 进入 `Edit` -> `Preferences` (或 `Wireshark` -> `Preferences` on macOS)
3. 展开 `Protocols` -> `TLS`
4. 在 `(Pre)-Master-Secret log filename` 字段中输入SSL密钥日志文件的完整路径
   - 例如: `C:\Users\<USER>\PycharmProjects\aioquic_qpack\ssl_keys.log`
5. 点击 `OK` 保存设置

### 2. 配置QUIC协议
1. 在Preferences中找到 `Protocols` -> `QUIC`
2. 确保启用了QUIC解析
3. 检查HTTP/3设置是否正确

### 3. 开始抓包
1. 选择正确的网络接口（通常是连接到互联网的接口）
2. 设置过滤器：`host aliyun.hawks.top` 或 `udp.port == 443`
3. 点击开始抓包

## 运行测试并抓包

### 1. 启动Wireshark抓包
```bash
# 设置过滤器
host aliyun.hawks.top and udp
```

### 2. 运行客户端
```bash
# 在另一个终端中运行
venv\Scripts\activate.ps1
python qpack_debug_client.py --host aliyun.hawks.top --requests 5 --insecure --secrets-log ssl_keys.log
```

### 3. 停止抓包
客户端运行完成后停止Wireshark抓包

## 分析HTTP/3和QPACK流量

### 1. 查找QUIC连接
- 在Wireshark中查找QUIC Initial包
- 跟踪QUIC连接的建立过程
- 查看TLS握手和ALPN协商（应该看到h3）

### 2. 分析HTTP/3流量
- 查找HTTP/3 HEADERS帧
- 查看QPACK编码的头部数据
- 观察SETTINGS帧中的QPACK设置

### 3. 观察QPACK动态表操作
在解密的流量中，您应该能看到：

#### 第一次请求
- QPACK编码器流上的动态表插入指令
- 大型头部字段的字面量编码
- 动态表条目的创建

#### 后续请求
- 对动态表索引的引用
- 显著减少的头部数据大小
- QPACK解码器流上的确认信息

### 4. 有用的Wireshark过滤器

```bash
# 显示所有QUIC流量
quic

# 显示HTTP/3流量
http3

# 显示特定流的数据
quic.stream_id == 0

# 显示QPACK编码器/解码器流
quic.stream_id == 2 or quic.stream_id == 3

# 显示HEADERS帧
http3.frame.type == 1

# 显示SETTINGS帧
http3.frame.type == 4
```

## 预期观察结果

### 1. 连接建立阶段
- QUIC Initial包交换
- TLS握手完成
- HTTP/3 SETTINGS帧交换
- QPACK设置协商

### 2. 第一次请求
- HTTP/3 HEADERS帧包含完整的头部字段
- QPACK编码器流可能包含动态表插入
- 较大的数据包大小

### 3. 后续请求
- HTTP/3 HEADERS帧显著变小
- 头部字段使用动态表引用
- 明显的数据包大小减少

## 故障排除

### 1. 无法解密流量
- 确认SSL密钥日志文件路径正确
- 检查文件是否有写入权限
- 确认Wireshark版本支持QUIC/HTTP3
- 重启Wireshark后重新加载密钥文件

### 2. 看不到HTTP/3流量
- 确认过滤器设置正确
- 检查是否抓取了正确的网络接口
- 确认服务器支持HTTP/3

### 3. QPACK数据无法解析
- 确认Wireshark版本 >= 3.4
- 检查QUIC协议解析器是否启用
- 确认TLS解密工作正常

## 高级分析技巧

### 1. 导出解密数据
- 右键点击解密的包
- 选择 "Follow" -> "QUIC Stream"
- 查看完整的应用层数据

### 2. 统计分析
- 使用 `Statistics` -> `Protocol Hierarchy` 查看协议分布
- 使用 `Statistics` -> `Conversations` 分析流量模式

### 3. 时间序列分析
- 使用 `Statistics` -> `I/O Graph` 查看流量时间分布
- 分析请求响应时间

## 示例命令

### 完整的抓包和分析流程
```bash
# 1. 启动Wireshark并配置TLS密钥日志

# 2. 开始抓包，设置过滤器
host aliyun.hawks.top

# 3. 运行客户端
python qpack_debug_client.py --host aliyun.hawks.top --requests 5 --insecure --secrets-log ssl_keys.log

# 4. 在Wireshark中分析结果
# 使用过滤器: http3 or quic.stream_id == 2 or quic.stream_id == 3
```

## 预期的QPACK压缩效果

通过Wireshark分析，您应该能观察到：

1. **第一次请求**: 大型头部字段以字面量形式传输
2. **后续请求**: 相同头部字段使用2-3字节的索引引用
3. **压缩率**: 通常可以达到60-90%的头部压缩率
4. **动态表**: 在QPACK编码器流中看到表项插入和管理

这种分析方法可以帮助您深入理解QPACK动态表的工作机制，以及HTTP/3在实际网络中的性能优势。
