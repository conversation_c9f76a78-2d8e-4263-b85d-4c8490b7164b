<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8"/>
        <title>aioquic</title>
        <link rel="stylesheet" href="/style.css"/>
    </head>
    <body>
        <h1>Welcome to aioquic</h1>
        <p>
            This is a test page for <a href="https://github.com/aiortc/aioquic/">aioquic</a>,
            a QUIC and HTTP/3 implementation written in Python.
        </p>
{% if request.scope["http_version"] == "3" %}
        <p>
            Congratulations, you loaded this page using HTTP/3!
        </p>
{% endif %}
        <h2>Available endpoints</h2>
        <ul>
            <li><strong>GET /</strong> returns the homepage</li>
            <li><strong>GET /NNNNN</strong> returns NNNNN bytes of plain text</li>
            <li><strong>POST /echo</strong> returns the request data</li>
            <li>
                <strong>CONNECT /ws</strong> runs a WebSocket echo service.
                You must set the <em>:protocol</em> pseudo-header to <em>"websocket"</em>.
            </li>
            <li>
                <strong>CONNECT /wt</strong> runs a WebTransport echo service.
                You must set the <em>:protocol</em> pseudo-header to <em>"webtransport"</em>.
            </li>
        </ul>
    </body>
</html>
