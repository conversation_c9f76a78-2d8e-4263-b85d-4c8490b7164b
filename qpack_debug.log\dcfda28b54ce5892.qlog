{"qlog_format": "JSON", "qlog_version": "0.3", "traces": [{"common_fields": {"ODCID": "dcfda28b54ce5892"}, "events": [{"data": {"new": "control", "stream_id": 2}, "name": "http:stream_type_set", "time": 1750836070306.4548}, {"data": {"new": "qpack_encoder", "stream_id": 6}, "name": "http:stream_type_set", "time": 1750836070306.4548}, {"data": {"new": "qpack_decoder", "stream_id": 10}, "name": "http:stream_type_set", "time": 1750836070306.4548}, {"data": {"client_versions": [1, 1798521807], "chosen_version": 1}, "name": "transport:version_information", "time": 1750836070306.4548}, {"data": {"client_alpns": ["h3"]}, "name": "transport:alpn_information", "time": 1750836070306.4548}, {"data": {"owner": "local", "max_idle_timeout": 60000, "initial_max_data": 1048576, "initial_max_stream_data_bidi_local": 1048576, "initial_max_stream_data_bidi_remote": 1048576, "initial_max_stream_data_uni": 1048576, "initial_max_streams_bidi": 128, "initial_max_streams_uni": 128, "ack_delay_exponent": 3, "max_ack_delay": 25, "disable_active_migration": false, "active_connection_id_limit": 8, "initial_source_connection_id": "f6fdd4a209d66549", "max_datagram_frame_size": 65536}, "name": "transport:parameters_set", "time": 1750836070307.4553}, {"data": {"cwnd": 12000, "bytes_in_flight": 531}, "name": "recovery:metrics_updated", "time": 1750836070313.151}, {"data": {"frames": [{"frame_type": "crypto", "length": 483, "offset": 0}], "header": {"packet_number": 0, "packet_type": "initial", "scid": "f6fdd4a209d66549", "dcid": "dcfda28b54ce5892"}, "raw": {"length": 531}}, "name": "transport:packet_sent", "time": 1750836070313.151}, {"data": {"count": 1, "raw": [{"length": 1208, "payload_length": 1200}]}, "name": "transport:datagrams_sent", "time": 1750836070313.151}, {"data": {"count": 1, "raw": [{"length": 67, "payload_length": 59}]}, "name": "transport:datagrams_received", "time": 1750836070344.8374}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 0]], "frame_type": "ack"}], "header": {"packet_number": 0, "packet_type": "initial", "dcid": "f6fdd4a209d66549", "scid": "****************************************"}, "raw": {"length": 59}}, "name": "transport:packet_received", "time": 1750836070344.8374}, {"data": {"cwnd": 12531, "bytes_in_flight": 0, "latest_rtt": 31.9999999992433, "min_rtt": 31.9999999992433, "smoothed_rtt": 31.9999999992433, "rtt_variance": 15.99999999962165}, "name": "recovery:metrics_updated", "time": 1750836070344.8374}, {"data": {"count": 1, "raw": [{"length": 1208, "payload_length": 1200}]}, "name": "transport:datagrams_received", "time": 1750836070352.2905}, {"data": {"frames": [{"frame_type": "crypto", "length": 123, "offset": 0}], "header": {"packet_number": 1, "packet_type": "initial", "dcid": "f6fdd4a209d66549", "scid": "****************************************"}, "raw": {"length": 182}}, "name": "transport:packet_received", "time": 1750836070352.2905}, {"data": {"key_type": "server_handshake_secret", "trigger": "tls"}, "name": "security:key_updated", "time": 1750836070352.8047}, {"data": {"frames": [{"frame_type": "crypto", "length": 143, "offset": 0}, {"frame_type": "crypto", "length": 812, "offset": 143}], "header": {"packet_number": 0, "packet_type": "handshake", "dcid": "f6fdd4a209d66549", "scid": "****************************************"}, "raw": {"length": 1018}}, "name": "transport:packet_received", "time": 1750836070352.8047}, {"data": {"owner": "remote", "original_destination_connection_id": "dcfda28b54ce5892", "max_idle_timeout": 60000, "stateless_reset_token": "5cbc326110ca9302d1e905c576e05258", "max_udp_payload_size": 65527, "initial_max_data": 137363456, "initial_max_stream_data_bidi_local": 1048576, "initial_max_stream_data_bidi_remote": 1048576, "initial_max_stream_data_uni": 1048576, "initial_max_streams_bidi": 128, "initial_max_streams_uni": 3, "ack_delay_exponent": 3, "max_ack_delay": 25, "disable_active_migration": false, "active_connection_id_limit": 2, "initial_source_connection_id": "****************************************"}, "name": "transport:parameters_set", "time": 1750836070352.8047}, {"data": {"key_type": "client_handshake_secret", "trigger": "tls"}, "name": "security:key_updated", "time": 1750836070352.8047}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 1]], "frame_type": "ack"}], "header": {"packet_number": 1, "packet_type": "initial", "scid": "f6fdd4a209d66549", "dcid": "****************************************"}, "raw": {"length": 61}}, "name": "transport:packet_sent", "time": 1750836070352.8047}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 0]], "frame_type": "ack"}], "header": {"packet_number": 2, "packet_type": "handshake", "scid": "f6fdd4a209d66549", "dcid": "****************************************"}, "raw": {"length": 60}}, "name": "transport:packet_sent", "time": 1750836070352.8047}, {"data": {"cwnd": 12531, "bytes_in_flight": 0}, "name": "recovery:metrics_updated", "time": 1750836070352.8047}, {"data": {"count": 1, "raw": [{"length": 1208, "payload_length": 1200}]}, "name": "transport:datagrams_sent", "time": 1750836070352.8047}, {"data": {"count": 1, "raw": [{"length": 1208, "payload_length": 1200}]}, "name": "transport:datagrams_received", "time": 1750836070353.3499}, {"data": {"frames": [{"frame_type": "crypto", "length": 1141, "offset": 955}], "header": {"packet_number": 1, "packet_type": "handshake", "dcid": "f6fdd4a209d66549", "scid": "****************************************"}, "raw": {"length": 1200}}, "name": "transport:packet_received", "time": 1750836070353.3499}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 1]], "frame_type": "ack"}], "header": {"packet_number": 3, "packet_type": "handshake", "scid": "f6fdd4a209d66549", "dcid": "****************************************"}, "raw": {"length": 60}}, "name": "transport:packet_sent", "time": 1750836070353.3499}, {"data": {"count": 1, "raw": [{"length": 68, "payload_length": 60}]}, "name": "transport:datagrams_sent", "time": 1750836070353.3499}, {"data": {"count": 1, "raw": [{"length": 1172, "payload_length": 1164}]}, "name": "transport:datagrams_received", "time": 1750836070353.3499}, {"data": {"frames": [{"frame_type": "crypto", "length": 796, "offset": 2096}, {"frame_type": "crypto", "length": 264, "offset": 2892}, {"frame_type": "crypto", "length": 36, "offset": 3156}], "header": {"packet_number": 2, "packet_type": "handshake", "dcid": "f6fdd4a209d66549", "scid": "****************************************"}, "raw": {"length": 1164}}, "name": "transport:packet_received", "time": 1750836070353.3499}, {"data": {"key_type": "server_1rtt_secret", "trigger": "tls"}, "name": "security:key_updated", "time": 1750836070353.874}, {"data": {"key_type": "client_1rtt_secret", "trigger": "tls"}, "name": "security:key_updated", "time": 1750836070353.874}, {"data": {"cwnd": 12531, "bytes_in_flight": 100}, "name": "recovery:metrics_updated", "time": 1750836070354.3972}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 2]], "frame_type": "ack"}, {"frame_type": "crypto", "length": 36, "offset": 0}], "header": {"packet_number": 4, "packet_type": "handshake", "scid": "f6fdd4a209d66549", "dcid": "****************************************"}, "raw": {"length": 100}}, "name": "transport:packet_sent", "time": 1750836070354.3972}, {"data": {"cwnd": 12531, "bytes_in_flight": 196}, "name": "recovery:metrics_updated", "time": 1750836070354.3972}, {"data": {"frames": [{"connection_id": "6d6c43d4843b4b80", "frame_type": "new_connection_id", "length": 8, "reset_token": "5b299983b99401f67827b4b4607658a6", "retire_prior_to": 0, "sequence_number": 1}, {"fin": false, "frame_type": "stream", "length": 15, "offset": 0, "stream_id": 2}, {"fin": false, "frame_type": "stream", "length": 1, "offset": 0, "stream_id": 6}, {"fin": false, "frame_type": "stream", "length": 1, "offset": 0, "stream_id": 10}], "header": {"packet_number": 5, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 96}}, "name": "transport:packet_sent", "time": 1750836070354.3972}, {"data": {"count": 1, "raw": [{"length": 204, "payload_length": 196}]}, "name": "transport:datagrams_sent", "time": 1750836070354.3972}, {"data": {"frame": {"frame_type": "headers", "headers": [{"name": ":method", "value": "GET"}, {"name": ":scheme", "value": "https"}, {"name": ":authority", "value": "aliyun.hawks.top:443"}, {"name": ":path", "value": "/"}, {"name": "user-agent", "value": "qpack-debug-client/aioquic-1.2.0"}, {"name": "cookie", "value": "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"}, {"name": "x-custom-auth-token", "value": "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"}, {"name": "x-request-id", "value": "req-1-1750836070"}]}, "length": 266, "stream_id": 0}, "name": "http:frame_created", "time": 1750836070354.3972}, {"data": {"cwnd": 12531, "bytes_in_flight": 508}, "name": "recovery:metrics_updated", "time": 1750836070355.0266}, {"data": {"frames": [{"fin": true, "frame_type": "stream", "length": 269, "offset": 0, "stream_id": 0}], "header": {"packet_number": 6, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 312}}, "name": "transport:packet_sent", "time": 1750836070355.0266}, {"data": {"count": 1, "raw": [{"length": 320, "payload_length": 312}]}, "name": "transport:datagrams_sent", "time": 1750836070355.0266}, {"data": {"count": 1, "raw": [{"length": 61, "payload_length": 53}]}, "name": "transport:datagrams_received", "time": 1750836070383.6638}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[5, 5]], "frame_type": "ack"}, {"fin": false, "frame_type": "stream", "length": 1, "offset": 0, "stream_id": 3}, {"fin": false, "frame_type": "stream", "length": 8, "offset": 1, "stream_id": 3}, {"fin": false, "frame_type": "stream", "length": 1, "offset": 0, "stream_id": 7}], "header": {"packet_number": 0, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 53}}, "name": "transport:packet_received", "time": 1750836070383.6638}, {"data": {"cwnd": 12627, "bytes_in_flight": 412, "latest_rtt": 31.9999999992433, "min_rtt": 31.9999999992433, "smoothed_rtt": 31.9999999992433, "rtt_variance": 11.999999999716238}, "name": "recovery:metrics_updated", "time": 1750836070384.6594}, {"data": {"new": "control", "stream_id": 3}, "name": "http:stream_type_set", "time": 1750836070384.6594}, {"data": {"new": "qpack_decoder", "stream_id": 7}, "name": "http:stream_type_set", "time": 1750836070384.6594}, {"data": {"cwnd": 12627, "bytes_in_flight": 459}, "name": "recovery:metrics_updated", "time": 1750836070384.6594}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 3, "offset": 1, "stream_id": 6}], "header": {"packet_number": 7, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 47}}, "name": "transport:packet_sent", "time": 1750836070384.6594}, {"data": {"count": 1, "raw": [{"length": 55, "payload_length": 47}]}, "name": "transport:datagrams_sent", "time": 1750836070384.6594}, {"data": {"count": 1, "raw": [{"length": 926, "payload_length": 918}]}, "name": "transport:datagrams_received", "time": 1750836070384.6594}, {"data": {"frames": [{"frame_type": "crypto", "length": 321, "offset": 0}, {"frame_type": "crypto", "length": 321, "offset": 321}, {"frame_type": "handshake_done"}, {"connection_id": "34bb20077615c93176225fbbca9cb46b60be58ca", "frame_type": "new_connection_id", "length": 20, "reset_token": "2112a28bccb8b9efd93965c8c275df91", "retire_prior_to": 0, "sequence_number": 1}, {"connection_id": "****************************************", "frame_type": "new_connection_id", "length": 20, "reset_token": "a1b420a169d1b93fc53257e075f571b2", "retire_prior_to": 0, "sequence_number": 2}, {"connection_id": "****************************************", "frame_type": "new_connection_id", "length": 20, "reset_token": "ebff4c605621945424f5bb06a9d33c05", "retire_prior_to": 0, "sequence_number": 3}, {"connection_id": "34bbfca2e0a01fdc29c9495148b16e40222f6aa7", "frame_type": "new_connection_id", "length": 20, "reset_token": "63a613a1d1a26d13a4f6f0a2f0dccdae", "retire_prior_to": 0, "sequence_number": 4}, {"connection_id": "34bb56c9f23a95499dffc5d5b7cdf0979f56035c", "frame_type": "new_connection_id", "length": 20, "reset_token": "8606380a7902a2f401cbcf67e7bbeed0", "retire_prior_to": 0, "sequence_number": 5}, {"connection_id": "34bbecabd8d03e2666e1614dca6247c0d888a3bd", "frame_type": "new_connection_id", "length": 20, "reset_token": "5865e35b2e5993c1011772de99901dec", "retire_prior_to": 0, "sequence_number": 6}], "header": {"packet_number": 1, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 918}}, "name": "transport:packet_received", "time": 1750836070384.6594}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070384.6594}, {"data": {"key_type": "server_handshake_secret", "trigger": "tls"}, "name": "security:key_retired", "time": 1750836070384.6594}, {"data": {"key_type": "client_handshake_secret", "trigger": "tls"}, "name": "security:key_retired", "time": 1750836070384.6594}, {"data": {"cwnd": 12627, "bytes_in_flight": 359}, "name": "recovery:metrics_updated", "time": 1750836070384.6594}, {"data": {"count": 1, "raw": [{"length": 40, "payload_length": 32}]}, "name": "transport:datagrams_received", "time": 1750836070409.9849}, {"data": {"frames": [{"ack_delay": 25.0, "acked_ranges": [[5, 6]], "frame_type": "ack"}], "header": {"packet_number": 2, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 32}}, "name": "transport:packet_received", "time": 1750836070409.9849}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070409.9849}, {"data": {"cwnd": 12939, "bytes_in_flight": 47, "latest_rtt": 46.999999998661224, "min_rtt": 31.9999999992433, "smoothed_rtt": 33.87499999917054, "rtt_variance": 12.749999999641659}, "name": "recovery:metrics_updated", "time": 1750836070409.9849}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 2]], "frame_type": "ack"}], "header": {"packet_number": 8, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 44}}, "name": "transport:packet_sent", "time": 1750836070409.9849}, {"data": {"count": 1, "raw": [{"length": 52, "payload_length": 44}]}, "name": "transport:datagrams_sent", "time": 1750836070409.9849}, {"data": {"count": 1, "raw": [{"length": 40, "payload_length": 32}]}, "name": "transport:datagrams_received", "time": 1750836070438.7385}, {"data": {"frames": [{"ack_delay": 25.0, "acked_ranges": [[5, 7]], "frame_type": "ack"}], "header": {"packet_number": 3, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 32}}, "name": "transport:packet_received", "time": 1750836070439.7366}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070439.7366}, {"data": {"cwnd": 12986, "bytes_in_flight": 0, "latest_rtt": 46.000000002095476, "min_rtt": 31.9999999992433, "smoothed_rtt": 35.39062499953616, "rtt_variance": 13.062500000444288}, "name": "recovery:metrics_updated", "time": 1750836070439.7366}, {"data": {"count": 1, "raw": [{"length": 1358, "payload_length": 1350}]}, "name": "transport:datagrams_received", "time": 1750836070483.8088}, {"data": {"frames": [{"frame_type": "ping"}, {"frame_type": "padding"}], "header": {"packet_number": 7, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1350}}, "name": "transport:packet_received", "time": 1750836070483.8088}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070483.8088}, {"data": {"frames": [{"ack_delay": 15.999999995983671, "acked_ranges": [[0, 3], [7, 7]], "frame_type": "ack"}], "header": {"packet_number": 9, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 47}}, "name": "transport:packet_sent", "time": 1750836070503.6648}, {"data": {"count": 1, "raw": [{"length": 55, "payload_length": 47}]}, "name": "transport:datagrams_sent", "time": 1750836070503.6648}, {"data": {"count": 1, "raw": [{"length": 1433, "payload_length": 1425}]}, "name": "transport:datagrams_received", "time": 1750836070633.035}, {"data": {"frames": [{"frame_type": "ping"}, {"frame_type": "padding"}], "header": {"packet_number": 8, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1425}}, "name": "transport:packet_received", "time": 1750836070634.0435}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070634.0435}, {"data": {"frames": [{"ack_delay": 14.999999999417923, "acked_ranges": [[0, 3], [7, 8]], "frame_type": "ack"}], "header": {"packet_number": 10, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 47}}, "name": "transport:packet_sent", "time": 1750836070659.5784}, {"data": {"count": 1, "raw": [{"length": 55, "payload_length": 47}]}, "name": "transport:datagrams_sent", "time": 1750836070659.5784}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070791.661}, {"data": {"frames": [{"frame_type": "ping"}, {"frame_type": "padding"}], "header": {"packet_number": 9, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070791.661}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070791.661}, {"data": {"frames": [{"ack_delay": 14.999999999417923, "acked_ranges": [[0, 3], [7, 9]], "frame_type": "ack"}], "header": {"packet_number": 11, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 47}}, "name": "transport:packet_sent", "time": 1750836070814.849}, {"data": {"count": 1, "raw": [{"length": 55, "payload_length": 47}]}, "name": "transport:datagrams_sent", "time": 1750836070814.849}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070888.6821}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1431, "offset": 0, "stream_id": 0}], "header": {"packet_number": 10, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070888.6821}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070888.6821}, {"data": {"frame": {"frame_type": "headers", "headers": [{"name": ":status", "value": "200"}, {"name": "server", "value": "Tengin<PERSON>"}, {"name": "content-type", "value": "text/html; charset=UTF-8"}, {"name": "date", "value": "Wed, 25 Jun 2025 07:21:12 GMT"}, {"name": "x-powered-by", "value": "PHP/7.4.33"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "ens-cache10.l2hk7[406,406,200-0,M], ens-cache36.l2hk7[408,0], ens-cache12.hk34[497,496,200-0,M], ens-cache16.hk34[500,0]"}, {"name": "ali-swift-global-savetime", "value": "1750836072"}, {"name": "x-cache", "value": "MISS TCP_MISS dirn:-2:-2"}, {"name": "x-swift-savetime", "value": "Wed, 25 Jun 2025 07:21:12 GMT"}, {"name": "x-swift-cachetime", "value": "0"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "timing-allow-origin", "value": "*"}, {"name": "eagle<PERSON>", "value": "a3b523a417508360717346111e"}]}, "length": 391, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836070889.6833}, {"data": {"frame": {"frame_type": "data"}, "length": 14028, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836070889.6833}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070889.6833}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 1431, "stream_id": 0}], "header": {"packet_number": 11, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070889.6833}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070889.6833}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070889.6833}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 2861, "stream_id": 0}], "header": {"packet_number": 12, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070889.6833}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070889.6833}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070889.6833}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 4291, "stream_id": 0}], "header": {"packet_number": 13, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070891.1904}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070891.1904}, {"data": {"count": 1, "raw": [{"length": 164, "payload_length": 156}]}, "name": "transport:datagrams_received", "time": 1750836070891.1904}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 124, "offset": 14301, "stream_id": 0}], "header": {"packet_number": 20, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 156}}, "name": "transport:packet_received", "time": 1750836070891.1904}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836070891.1904}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070891.7153}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 5721, "stream_id": 0}], "header": {"packet_number": 14, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070891.7153}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070891.7153}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 7151, "stream_id": 0}], "header": {"packet_number": 15, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070891.7153}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070891.7153}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 8581, "stream_id": 0}], "header": {"packet_number": 16, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070891.7153}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070892.7236}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 10011, "stream_id": 0}], "header": {"packet_number": 17, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070892.7236}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070893.0942}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 11441, "stream_id": 0}], "header": {"packet_number": 18, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070893.0942}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836070893.0942}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 12871, "stream_id": 0}], "header": {"packet_number": 19, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836070893.0942}, {"data": {"frames": [{"ack_delay": 14.999999999417923, "acked_ranges": [[0, 3], [7, 20]], "frame_type": "ack"}], "header": {"packet_number": 12, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 47}}, "name": "transport:packet_sent", "time": 1750836070908.7512}, {"data": {"count": 1, "raw": [{"length": 55, "payload_length": 47}]}, "name": "transport:datagrams_sent", "time": 1750836070908.7512}, {"data": {"count": 1, "raw": [{"length": 37, "payload_length": 29}]}, "name": "transport:datagrams_received", "time": 1750836071035.8096}, {"data": {"frames": [{"frame_type": "ping"}, {"frame_type": "padding"}], "header": {"packet_number": 23, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 29}}, "name": "transport:packet_received", "time": 1750836071035.8096}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071035.8096}, {"data": {"count": 1, "raw": [{"length": 37, "payload_length": 29}]}, "name": "transport:datagrams_received", "time": 1750836071035.8096}, {"data": {"frames": [{"frame_type": "ping"}, {"frame_type": "padding"}], "header": {"packet_number": 24, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 29}}, "name": "transport:packet_received", "time": 1750836071035.8096}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071035.8096}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071048.363}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 14425, "stream_id": 0}], "header": {"packet_number": 26, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071048.363}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071048.363}, {"data": {"frame": {"frame_type": "data"}, "length": 5834, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836071048.363}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 3], [7, 20], [23, 24], [26, 26]], "frame_type": "ack"}], "header": {"packet_number": 13, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 50}}, "name": "transport:packet_sent", "time": 1750836071049.3682}, {"data": {"count": 1, "raw": [{"length": 58, "payload_length": 50}]}, "name": "transport:datagrams_sent", "time": 1750836071049.3682}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071049.3682}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 15855, "stream_id": 0}], "header": {"packet_number": 27, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071049.3682}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071049.3682}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071049.3682}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 17285, "stream_id": 0}], "header": {"packet_number": 28, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071049.3682}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071049.3682}, {"data": {"count": 1, "raw": [{"length": 163, "payload_length": 155}]}, "name": "transport:datagrams_received", "time": 1750836071050.8784}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 121, "offset": 20141, "stream_id": 0}], "header": {"packet_number": 30, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 155}}, "name": "transport:packet_received", "time": 1750836071050.8784}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071050.8784}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071051.2412}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 18713, "stream_id": 0}], "header": {"packet_number": 29, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071051.2412}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836071051.8113}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 20262, "stream_id": 0}], "header": {"packet_number": 31, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836071051.8113}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071051.8113}, {"data": {"frame": {"frame_type": "data"}, "length": 22646, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836071051.8113}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836071051.8113}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 21688, "stream_id": 0}], "header": {"packet_number": 32, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836071051.8113}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071051.8113}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836071051.8113}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 23114, "stream_id": 0}], "header": {"packet_number": 33, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836071052.8223}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071052.8223}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836071052.8223}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 24540, "stream_id": 0}], "header": {"packet_number": 34, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836071052.8223}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071052.8223}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836071052.8223}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 25966, "stream_id": 0}], "header": {"packet_number": 35, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836071052.8223}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071052.8223}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071052.8223}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 27392, "stream_id": 0}], "header": {"packet_number": 36, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071052.8223}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071053.8198}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071053.8198}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 28820, "stream_id": 0}], "header": {"packet_number": 37, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071053.8198}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071053.8198}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071054.8154}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 30248, "stream_id": 0}], "header": {"packet_number": 38, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071054.8154}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071054.8154}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 3], [7, 20], [23, 24], [26, 38]], "frame_type": "ack"}], "header": {"packet_number": 14, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 50}}, "name": "transport:packet_sent", "time": 1750836071054.8154}, {"data": {"count": 1, "raw": [{"length": 58, "payload_length": 50}]}, "name": "transport:datagrams_sent", "time": 1750836071054.8154}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071054.8154}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 31676, "stream_id": 0}], "header": {"packet_number": 39, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071054.8154}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071054.8154}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071056.8203}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 33104, "stream_id": 0}], "header": {"packet_number": 40, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071056.8203}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071056.8203}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071064.8477}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 34532, "stream_id": 0}], "header": {"packet_number": 41, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071064.8477}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071064.8477}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071064.8477}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 35960, "stream_id": 0}], "header": {"packet_number": 42, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071064.8477}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071064.8477}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071065.912}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 37388, "stream_id": 0}], "header": {"packet_number": 43, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071065.912}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071065.912}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071065.912}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 38816, "stream_id": 0}], "header": {"packet_number": 44, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071065.912}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071065.912}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071065.912}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 40244, "stream_id": 0}], "header": {"packet_number": 45, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071066.9211}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071066.9211}, {"data": {"count": 1, "raw": [{"length": 1283, "payload_length": 1275}]}, "name": "transport:datagrams_received", "time": 1750836071066.9211}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1241, "offset": 41672, "stream_id": 0}], "header": {"packet_number": 46, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1275}}, "name": "transport:packet_received", "time": 1750836071066.9211}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071066.9211}, {"data": {"frames": [{"ack_delay": 15.999999995983671, "acked_ranges": [[0, 3], [7, 20], [23, 24], [26, 46]], "frame_type": "ack"}], "header": {"packet_number": 15, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 51}}, "name": "transport:packet_sent", "time": 1750836071082.9854}, {"data": {"count": 1, "raw": [{"length": 59, "payload_length": 51}]}, "name": "transport:datagrams_sent", "time": 1750836071082.9854}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071216.6443}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 42913, "stream_id": 0}], "header": {"packet_number": 47, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071216.6443}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071216.6443}, {"data": {"frame": {"frame_type": "data"}, "length": 7294, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836071216.6443}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071216.6443}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 44341, "stream_id": 0}], "header": {"packet_number": 48, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071216.6443}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071216.6443}, {"data": {"count": 1, "raw": [{"length": 199, "payload_length": 191}]}, "name": "transport:datagrams_received", "time": 1750836071216.6443}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 157, "offset": 50053, "stream_id": 0}], "header": {"packet_number": 52, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 191}}, "name": "transport:packet_received", "time": 1750836071216.6443}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071216.6443}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071216.6443}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 45769, "stream_id": 0}], "header": {"packet_number": 49, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071216.6443}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071216.6443}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 47197, "stream_id": 0}], "header": {"packet_number": 50, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071216.6443}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071216.6443}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 48625, "stream_id": 0}], "header": {"packet_number": 51, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071216.6443}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071216.6443}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 50210, "stream_id": 0}], "header": {"packet_number": 53, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071216.6443}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071216.6443}, {"data": {"frame": {"frame_type": "data"}, "length": 9084, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836071216.6443}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071217.6494}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 51638, "stream_id": 0}], "header": {"packet_number": 54, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071217.6494}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071217.6494}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071217.6494}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 53066, "stream_id": 0}], "header": {"packet_number": 55, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071217.6494}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071217.6494}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071217.6494}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 54494, "stream_id": 0}], "header": {"packet_number": 56, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071217.6494}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071217.6494}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071217.6494}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 55922, "stream_id": 0}], "header": {"packet_number": 57, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071217.6494}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071217.6494}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071217.6494}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 57350, "stream_id": 0}], "header": {"packet_number": 59, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071217.6494}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071217.6494}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071219.6448}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 519, "offset": 58778, "stream_id": 0}, {"fin": false, "frame_type": "stream", "length": 901, "offset": 59297, "stream_id": 0}], "header": {"packet_number": 60, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071219.6448}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071219.6448}, {"data": {"frame": {"frame_type": "data"}, "length": 8754, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836071219.6448}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071221.1487}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 60198, "stream_id": 0}], "header": {"packet_number": 61, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071221.1487}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071221.1487}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071221.1487}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 61626, "stream_id": 0}], "header": {"packet_number": 62, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071221.1487}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071221.1487}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071222.6758}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 63054, "stream_id": 0}], "header": {"packet_number": 63, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071222.6758}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071222.6758}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071223.6692}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 64482, "stream_id": 0}], "header": {"packet_number": 64, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071223.6692}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071223.6692}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071225.6729}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 65910, "stream_id": 0}], "header": {"packet_number": 65, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071225.6729}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071225.6729}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071226.6697}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 716, "offset": 67338, "stream_id": 0}, {"fin": false, "frame_type": "stream", "length": 704, "offset": 68054, "stream_id": 0}], "header": {"packet_number": 66, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071226.6697}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071226.6697}, {"data": {"frame": {"frame_type": "data"}, "length": 7630, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836071226.6697}, {"data": {"cwnd": 12986, "bytes_in_flight": 54}, "name": "recovery:metrics_updated", "time": 1750836071227.6729}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[0, 3], [7, 20], [23, 24], [26, 57], [59, 66]], "frame_type": "ack"}, {"frame_type": "ping"}], "header": {"packet_number": 16, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 54}}, "name": "transport:packet_sent", "time": 1750836071227.6729}, {"data": {"count": 1, "raw": [{"length": 62, "payload_length": 54}]}, "name": "transport:datagrams_sent", "time": 1750836071227.6729}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071227.6729}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 68758, "stream_id": 0}], "header": {"packet_number": 67, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071227.6729}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071227.6729}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071228.6729}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 70186, "stream_id": 0}], "header": {"packet_number": 68, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071228.6729}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071228.6729}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071229.6702}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 71614, "stream_id": 0}], "header": {"packet_number": 69, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071229.6702}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071229.6702}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071229.6702}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 73042, "stream_id": 0}], "header": {"packet_number": 70, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071229.6702}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071229.6702}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836071231.1748}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1217, "offset": 74470, "stream_id": 0}, {"fin": false, "frame_type": "stream", "length": 203, "offset": 75687, "stream_id": 0}], "header": {"packet_number": 71, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836071231.1748}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071231.1748}, {"data": {"frame": {"frame_type": "data"}, "length": 427, "stream_id": 0}, "name": "http:frame_parsed", "time": 1750836071231.1748}, {"data": {"count": 1, "raw": [{"length": 276, "payload_length": 268}]}, "name": "transport:datagrams_received", "time": 1750836071231.7236}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 227, "offset": 75890, "stream_id": 0}, {"fin": true, "frame_type": "stream", "length": 0, "offset": 76117, "stream_id": 0}], "header": {"packet_number": 72, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 268}}, "name": "transport:packet_received", "time": 1750836071231.7236}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071231.7236}, {"data": {"frames": [{"ack_delay": 15.999999995983671, "acked_ranges": [[0, 3], [7, 20], [23, 24], [26, 57], [59, 72]], "frame_type": "ack"}], "header": {"packet_number": 17, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 54}}, "name": "transport:packet_sent", "time": 1750836071251.636}, {"data": {"count": 1, "raw": [{"length": 62, "payload_length": 54}]}, "name": "transport:datagrams_sent", "time": 1750836071251.636}, {"data": {"count": 1, "raw": [{"length": 39, "payload_length": 31}]}, "name": "transport:datagrams_received", "time": 1750836071280.9448}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[6, 17]], "frame_type": "ack"}], "header": {"packet_number": 73, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 31}}, "name": "transport:packet_received", "time": 1750836071280.9448}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071280.9448}, {"data": {"cwnd": 13040, "bytes_in_flight": 0, "latest_rtt": 31.000000002677552, "min_rtt": 31.000000002677552, "smoothed_rtt": 34.84179687492883, "rtt_variance": 9.796875000333216}, "name": "recovery:metrics_updated", "time": 1750836071280.9448}, {"data": {"count": 1, "raw": [{"length": 37, "payload_length": 29}]}, "name": "transport:datagrams_received", "time": 1750836071281.9841}, {"data": {"frames": [{"frame_type": "max_streams", "maximum": 129, "stream_type": "bidirectional"}], "header": {"packet_number": 74, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 29}}, "name": "transport:packet_received", "time": 1750836071281.9841}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071281.9841}, {"data": {"frames": [{"ack_delay": 16.00000000325963, "acked_ranges": [[73, 74]], "frame_type": "ack"}], "header": {"packet_number": 18, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836071297.3135}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836071297.3135}, {"data": {"frame": {"frame_type": "headers", "headers": [{"name": ":method", "value": "GET"}, {"name": ":scheme", "value": "https"}, {"name": ":authority", "value": "aliyun.hawks.top:443"}, {"name": ":path", "value": "/"}, {"name": "user-agent", "value": "qpack-debug-client/aioquic-1.2.0"}, {"name": "cookie", "value": "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"}, {"name": "x-custom-auth-token", "value": "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"}, {"name": "x-request-id", "value": "req-2-1750836071"}]}, "length": 266, "stream_id": 4}, "name": "http:frame_created", "time": 1750836071744.8518}, {"data": {"cwnd": 13040, "bytes_in_flight": 312}, "name": "recovery:metrics_updated", "time": 1750836071744.8518}, {"data": {"frames": [{"fin": true, "frame_type": "stream", "length": 269, "offset": 0, "stream_id": 4}], "header": {"packet_number": 19, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 312}}, "name": "transport:packet_sent", "time": 1750836071744.8518}, {"data": {"count": 1, "raw": [{"length": 320, "payload_length": 312}]}, "name": "transport:datagrams_sent", "time": 1750836071744.8518}, {"data": {"count": 1, "raw": [{"length": 40, "payload_length": 32}]}, "name": "transport:datagrams_received", "time": 1750836071811.0063}, {"data": {"frames": [{"ack_delay": 34.0, "acked_ranges": [[6, 19]], "frame_type": "ack"}], "header": {"packet_number": 75, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 32}}, "name": "transport:packet_received", "time": 1750836071811.0063}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836071811.0063}, {"data": {"cwnd": 13352, "bytes_in_flight": 0, "latest_rtt": 37.000000005355105, "min_rtt": 31.000000002677552, "smoothed_rtt": 35.111572266232116, "rtt_variance": 8.8476562509193}, "name": "recovery:metrics_updated", "time": 1750836071811.0063}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072089.8713}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1431, "offset": 0, "stream_id": 4}], "header": {"packet_number": 76, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072089.8713}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072089.8713}, {"data": {"frame": {"frame_type": "headers", "headers": [{"name": ":status", "value": "200"}, {"name": "server", "value": "Tengin<PERSON>"}, {"name": "content-type", "value": "text/html; charset=UTF-8"}, {"name": "date", "value": "Wed, 25 Jun 2025 07:21:13 GMT"}, {"name": "x-powered-by", "value": "PHP/7.4.33"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "ens-cache10.l2hk7[305,305,200-0,M], ens-cache7.l2hk7[308,0], ens-cache12.hk34[311,311,200-0,M], ens-cache16.hk34[314,0]"}, {"name": "ali-swift-global-savetime", "value": "1750836073"}, {"name": "x-cache", "value": "MISS TCP_MISS dirn:-2:-2"}, {"name": "x-swift-savetime", "value": "Wed, 25 Jun 2025 07:21:13 GMT"}, {"name": "x-swift-cachetime", "value": "0"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "timing-allow-origin", "value": "*"}, {"name": "eagle<PERSON>", "value": "a3b523a417508360731253862e"}]}, "length": 391, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072089.8713}, {"data": {"frame": {"frame_type": "data"}, "length": 14028, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072089.8713}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072090.8752}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 1431, "stream_id": 4}], "header": {"packet_number": 77, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072090.8752}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072090.8752}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072090.8752}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 2861, "stream_id": 4}], "header": {"packet_number": 78, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072090.8752}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072090.8752}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072091.386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 4291, "stream_id": 4}], "header": {"packet_number": 79, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072091.386}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072091.386}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072091.386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 5721, "stream_id": 4}], "header": {"packet_number": 80, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072091.386}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072091.386}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072091.386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 7151, "stream_id": 4}], "header": {"packet_number": 81, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072091.386}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072091.386}, {"data": {"count": 1, "raw": [{"length": 164, "payload_length": 156}]}, "name": "transport:datagrams_received", "time": 1750836072091.386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 124, "offset": 14301, "stream_id": 4}], "header": {"packet_number": 86, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 156}}, "name": "transport:packet_received", "time": 1750836072091.386}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072091.386}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072091.386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 8581, "stream_id": 4}], "header": {"packet_number": 82, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072091.386}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072091.8936}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 10011, "stream_id": 4}], "header": {"packet_number": 83, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072091.8936}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072091.8936}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 11441, "stream_id": 4}], "header": {"packet_number": 84, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072091.8936}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072091.8936}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 12871, "stream_id": 4}], "header": {"packet_number": 85, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072091.8936}, {"data": {"frames": [{"ack_delay": 14.999999999417923, "acked_ranges": [[75, 86]], "frame_type": "ack"}], "header": {"packet_number": 20, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836072103.0857}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836072103.0857}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072238.1243}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 14425, "stream_id": 4}], "header": {"packet_number": 87, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072238.1243}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072238.1243}, {"data": {"frame": {"frame_type": "data"}, "length": 5696, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072238.1243}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072238.1243}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 15855, "stream_id": 4}], "header": {"packet_number": 88, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072239.1575}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072239.1575}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072239.1575}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 17285, "stream_id": 4}], "header": {"packet_number": 89, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072239.1575}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072239.1575}, {"data": {"count": 1, "raw": [{"length": 1453, "payload_length": 1445}]}, "name": "transport:datagrams_received", "time": 1750836072239.1575}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1411, "offset": 18713, "stream_id": 4}], "header": {"packet_number": 90, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1445}}, "name": "transport:packet_received", "time": 1750836072239.1575}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072239.1575}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072240.2078}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 20124, "stream_id": 4}], "header": {"packet_number": 91, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072240.2078}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072240.2078}, {"data": {"frame": {"frame_type": "data"}, "length": 8754, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072240.2078}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072240.2078}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 21552, "stream_id": 4}], "header": {"packet_number": 92, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072240.2078}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072240.2078}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072240.2078}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 22980, "stream_id": 4}], "header": {"packet_number": 93, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072241.1875}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072241.1875}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072241.1875}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 24408, "stream_id": 4}], "header": {"packet_number": 94, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072241.1875}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072241.1875}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072241.703}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 25836, "stream_id": 4}], "header": {"packet_number": 95, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072241.703}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072241.703}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072241.703}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 27264, "stream_id": 4}], "header": {"packet_number": 96, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072241.703}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072241.703}, {"data": {"count": 1, "raw": [{"length": 231, "payload_length": 223}]}, "name": "transport:datagrams_received", "time": 1750836072241.703}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 189, "offset": 28692, "stream_id": 4}], "header": {"packet_number": 97, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 223}}, "name": "transport:packet_received", "time": 1750836072241.703}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072241.703}, {"data": {"frames": [{"ack_delay": 15.999999995983671, "acked_ranges": [[75, 97]], "frame_type": "ack"}], "header": {"packet_number": 21, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836072242.7783}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836072242.7783}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072242.7783}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 28881, "stream_id": 4}], "header": {"packet_number": 98, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"frame": {"frame_type": "data"}, "length": 14030, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 30309, "stream_id": 4}], "header": {"packet_number": 99, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 31737, "stream_id": 4}], "header": {"packet_number": 100, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 33165, "stream_id": 4}], "header": {"packet_number": 101, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 34593, "stream_id": 4}], "header": {"packet_number": 102, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 36021, "stream_id": 4}], "header": {"packet_number": 103, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 37449, "stream_id": 4}], "header": {"packet_number": 104, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 38877, "stream_id": 4}], "header": {"packet_number": 105, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1223, "payload_length": 1215}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1181, "offset": 41733, "stream_id": 4}], "header": {"packet_number": 107, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1215}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072243.4956}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072243.4956}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 40305, "stream_id": 4}], "header": {"packet_number": 106, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072243.4956}, {"data": {"frames": [{"ack_delay": 16.00000000325963, "acked_ranges": [[75, 107]], "frame_type": "ack"}], "header": {"packet_number": 22, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836072258.438}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836072258.438}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072391.0784}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 42914, "stream_id": 4}], "header": {"packet_number": 108, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072391.0784}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072391.0784}, {"data": {"frame": {"frame_type": "data"}, "length": 11674, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072391.0784}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072391.0784}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 44342, "stream_id": 4}], "header": {"packet_number": 109, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072391.0784}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072391.0784}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072392.086}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 45770, "stream_id": 4}], "header": {"packet_number": 110, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072392.086}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072392.086}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072392.086}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 47198, "stream_id": 4}], "header": {"packet_number": 111, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072392.086}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072392.086}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072392.086}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 48626, "stream_id": 4}], "header": {"packet_number": 112, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072392.086}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072392.086}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072393.2422}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 50054, "stream_id": 4}], "header": {"packet_number": 113, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072393.2422}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072393.2422}, {"data": {"count": 1, "raw": [{"length": 295, "payload_length": 287}]}, "name": "transport:datagrams_received", "time": 1750836072393.6587}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 253, "offset": 54338, "stream_id": 4}], "header": {"packet_number": 116, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 287}}, "name": "transport:packet_received", "time": 1750836072393.6587}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072393.6587}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072394.2412}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 51482, "stream_id": 4}], "header": {"packet_number": 114, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072394.2412}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072394.2412}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 52910, "stream_id": 4}], "header": {"packet_number": 115, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072394.2412}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836072394.2412}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 54591, "stream_id": 4}], "header": {"packet_number": 117, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836072394.2412}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072394.2412}, {"data": {"frame": {"frame_type": "data"}, "length": 21081, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072394.2412}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836072395.2412}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 56017, "stream_id": 4}], "header": {"packet_number": 118, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836072395.2412}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072395.2412}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836072395.2412}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 57443, "stream_id": 4}], "header": {"packet_number": 119, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836072395.2412}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072395.2412}, {"data": {"count": 1, "raw": [{"length": 1468, "payload_length": 1460}]}, "name": "transport:datagrams_received", "time": 1750836072395.2412}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1426, "offset": 58869, "stream_id": 4}], "header": {"packet_number": 120, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1460}}, "name": "transport:packet_received", "time": 1750836072395.2412}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072395.2412}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072396.241}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 60295, "stream_id": 4}], "header": {"packet_number": 121, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072396.241}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072396.241}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072396.241}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 61723, "stream_id": 4}], "header": {"packet_number": 122, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072396.241}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072396.241}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072396.241}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 63151, "stream_id": 4}], "header": {"packet_number": 123, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072396.241}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072396.241}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072396.241}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 64579, "stream_id": 4}], "header": {"packet_number": 124, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072396.241}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072396.241}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072396.241}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 66007, "stream_id": 4}], "header": {"packet_number": 125, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072396.241}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072396.241}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072396.241}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 67435, "stream_id": 4}], "header": {"packet_number": 126, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072397.6128}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072397.6128}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072397.6128}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 68863, "stream_id": 4}], "header": {"packet_number": 127, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072397.6128}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072397.6128}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072397.6128}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 70291, "stream_id": 4}], "header": {"packet_number": 128, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072397.6128}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072397.6128}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072398.3774}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 71719, "stream_id": 4}], "header": {"packet_number": 129, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072398.3774}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072398.3774}, {"data": {"frames": [{"ack_delay": 0.0, "acked_ranges": [[75, 129]], "frame_type": "ack"}], "header": {"packet_number": 23, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 45}}, "name": "transport:packet_sent", "time": 1750836072398.3774}, {"data": {"count": 1, "raw": [{"length": 53, "payload_length": 45}]}, "name": "transport:datagrams_sent", "time": 1750836072398.3774}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072399.3823}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 73147, "stream_id": 4}], "header": {"packet_number": 130, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072399.3823}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072399.3823}, {"data": {"count": 1, "raw": [{"length": 168, "payload_length": 160}]}, "name": "transport:datagrams_received", "time": 1750836072400.8943}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 119, "offset": 75995, "stream_id": 4}, {"fin": true, "frame_type": "stream", "length": 0, "offset": 76114, "stream_id": 4}], "header": {"packet_number": 132, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 160}}, "name": "transport:packet_received", "time": 1750836072400.8943}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072400.8943}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836072400.8943}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1102, "offset": 74575, "stream_id": 4}, {"fin": false, "frame_type": "stream", "length": 318, "offset": 75677, "stream_id": 4}], "header": {"packet_number": 131, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836072400.8943}, {"data": {"frame": {"frame_type": "data"}, "length": 434, "stream_id": 4}, "name": "http:frame_parsed", "time": 1750836072400.8943}, {"data": {"frames": [{"ack_delay": 16.00000000325963, "acked_ranges": [[75, 132]], "frame_type": "ack"}], "header": {"packet_number": 24, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836072428.8467}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836072428.8467}, {"data": {"count": 1, "raw": [{"length": 37, "payload_length": 29}]}, "name": "transport:datagrams_received", "time": 1750836072459.576}, {"data": {"frames": [{"frame_type": "max_streams", "maximum": 130, "stream_type": "bidirectional"}], "header": {"packet_number": 133, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 29}}, "name": "transport:packet_received", "time": 1750836072459.576}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072459.576}, {"data": {"frames": [{"ack_delay": 16.00000000325963, "acked_ranges": [[75, 133]], "frame_type": "ack"}], "header": {"packet_number": 25, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836072474.5051}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836072474.5051}, {"data": {"frame": {"frame_type": "headers", "headers": [{"name": ":method", "value": "GET"}, {"name": ":scheme", "value": "https"}, {"name": ":authority", "value": "aliyun.hawks.top:443"}, {"name": ":path", "value": "/"}, {"name": "user-agent", "value": "qpack-debug-client/aioquic-1.2.0"}, {"name": "cookie", "value": "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"}, {"name": "x-custom-auth-token", "value": "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"}, {"name": "x-request-id", "value": "req-3-1750836072"}]}, "length": 23, "stream_id": 8}, "name": "http:frame_created", "time": 1750836072921.3938}, {"data": {"cwnd": 13352, "bytes_in_flight": 320}, "name": "recovery:metrics_updated", "time": 1750836072921.3938}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 247, "offset": 4, "stream_id": 6}, {"fin": true, "frame_type": "stream", "length": 25, "offset": 0, "stream_id": 8}], "header": {"packet_number": 26, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 320}}, "name": "transport:packet_sent", "time": 1750836072921.3938}, {"data": {"count": 1, "raw": [{"length": 328, "payload_length": 320}]}, "name": "transport:datagrams_sent", "time": 1750836072921.3938}, {"data": {"count": 1, "raw": [{"length": 39, "payload_length": 31}]}, "name": "transport:datagrams_received", "time": 1750836072953.7427}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1, "offset": 1, "stream_id": 7}], "header": {"packet_number": 134, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 31}}, "name": "transport:packet_received", "time": 1750836072953.7427}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072953.7427}, {"data": {"frames": [{"ack_delay": 16.00000000325963, "acked_ranges": [[75, 134]], "frame_type": "ack"}], "header": {"packet_number": 27, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836072966.7715}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836072966.7715}, {"data": {"count": 1, "raw": [{"length": 40, "payload_length": 32}]}, "name": "transport:datagrams_received", "time": 1750836072977.8037}, {"data": {"frames": [{"ack_delay": 25.0, "acked_ranges": [[6, 26]], "frame_type": "ack"}], "header": {"packet_number": 135, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 32}}, "name": "transport:packet_received", "time": 1750836072977.8037}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836072977.8037}, {"data": {"cwnd": 13672, "bytes_in_flight": 0, "latest_rtt": 36.99999999807915, "min_rtt": 31.000000002677552, "smoothed_rtt": 35.34762573271299, "rtt_variance": 8.135742187039872}, "name": "recovery:metrics_updated", "time": 1750836072977.8037}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073266.724}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1431, "offset": 0, "stream_id": 8}], "header": {"packet_number": 136, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073267.7363}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073267.7363}, {"data": {"frame": {"frame_type": "headers", "headers": [{"name": ":status", "value": "200"}, {"name": "server", "value": "Tengin<PERSON>"}, {"name": "content-type", "value": "text/html; charset=UTF-8"}, {"name": "date", "value": "Wed, 25 Jun 2025 07:21:14 GMT"}, {"name": "x-powered-by", "value": "PHP/7.4.33"}, {"name": "vary", "value": "Accept-Encoding"}, {"name": "via", "value": "ens-cache10.l2hk7[306,305,200-0,M], ens-cache22.l2hk7[307,0], ens-cache12.hk34[312,311,200-0,M], ens-cache16.hk34[314,0]"}, {"name": "ali-swift-global-savetime", "value": "1750836074"}, {"name": "x-cache", "value": "MISS TCP_MISS dirn:-2:-2"}, {"name": "x-swift-savetime", "value": "Wed, 25 Jun 2025 07:21:14 GMT"}, {"name": "x-swift-cachetime", "value": "0"}, {"name": "alt-svc", "value": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000"}, {"name": "timing-allow-origin", "value": "*"}, {"name": "eagle<PERSON>", "value": "a3b523a417508360743028487e"}]}, "length": 392, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073267.7363}, {"data": {"frame": {"frame_type": "data"}, "length": 3730, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073267.7363}, {"data": {"count": 1, "raw": [{"length": 1307, "payload_length": 1299}]}, "name": "transport:datagrams_received", "time": 1750836073267.7363}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1267, "offset": 2861, "stream_id": 8}], "header": {"packet_number": 138, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1299}}, "name": "transport:packet_received", "time": 1750836073267.7363}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073267.7363}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073267.7363}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 1431, "stream_id": 8}], "header": {"packet_number": 137, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073267.7363}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073267.7363}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 4128, "stream_id": 8}], "header": {"packet_number": 139, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073267.7363}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073267.7363}, {"data": {"frame": {"frame_type": "data"}, "length": 10298, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073267.7363}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073267.7363}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 5558, "stream_id": 8}], "header": {"packet_number": 140, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073267.7363}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073267.7363}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073267.7363}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 6988, "stream_id": 8}], "header": {"packet_number": 141, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073267.7363}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073267.7363}, {"data": {"count": 1, "raw": [{"length": 331, "payload_length": 323}]}, "name": "transport:datagrams_received", "time": 1750836073267.7363}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 291, "offset": 14138, "stream_id": 8}], "header": {"packet_number": 146, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 323}}, "name": "transport:packet_received", "time": 1750836073267.7363}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073267.7363}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073268.6624}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 8418, "stream_id": 8}], "header": {"packet_number": 142, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073268.6624}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073268.6624}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 9848, "stream_id": 8}], "header": {"packet_number": 143, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073268.6624}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073268.6624}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 11278, "stream_id": 8}], "header": {"packet_number": 144, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073268.6624}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073268.6624}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 12708, "stream_id": 8}], "header": {"packet_number": 145, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073268.6624}, {"data": {"frames": [{"ack_delay": 14.999999999417923, "acked_ranges": [[134, 146]], "frame_type": "ack"}], "header": {"packet_number": 28, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836073277.9294}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836073277.9294}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073422.7148}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 14429, "stream_id": 8}], "header": {"packet_number": 147, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073422.7148}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073422.7148}, {"data": {"frame": {"frame_type": "data"}, "length": 8754, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073422.7148}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073423.718}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1430, "offset": 15859, "stream_id": 8}], "header": {"packet_number": 148, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073423.718}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073423.718}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073423.718}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 17289, "stream_id": 8}], "header": {"packet_number": 149, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073423.718}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073423.718}, {"data": {"count": 1, "raw": [{"length": 227, "payload_length": 219}]}, "name": "transport:datagrams_received", "time": 1750836073424.7178}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 185, "offset": 23001, "stream_id": 8}], "header": {"packet_number": 153, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 219}}, "name": "transport:packet_received", "time": 1750836073424.7178}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073424.7178}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073424.7178}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 18717, "stream_id": 8}], "header": {"packet_number": 150, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073424.7178}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073424.7178}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 20145, "stream_id": 8}], "header": {"packet_number": 151, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073425.7175}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073425.7175}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 21573, "stream_id": 8}], "header": {"packet_number": 152, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073425.7175}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073425.7175}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 23186, "stream_id": 8}], "header": {"packet_number": 154, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073425.7175}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073425.7175}, {"data": {"frame": {"frame_type": "data"}, "length": 7624, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073425.7175}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073426.7183}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 24614, "stream_id": 8}], "header": {"packet_number": 155, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073426.7183}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073426.7183}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073426.7183}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 26042, "stream_id": 8}], "header": {"packet_number": 156, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073426.7183}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073426.7183}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073427.7183}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 27470, "stream_id": 8}], "header": {"packet_number": 157, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073427.7183}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073427.7183}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073427.7183}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 28898, "stream_id": 8}], "header": {"packet_number": 158, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073427.7183}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073427.7183}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073427.7183}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 487, "offset": 30326, "stream_id": 8}, {"fin": false, "frame_type": "stream", "length": 933, "offset": 30813, "stream_id": 8}], "header": {"packet_number": 159, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073427.7183}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073427.7183}, {"data": {"frame": {"frame_type": "data"}, "length": 7294, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073428.718}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073428.718}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 31746, "stream_id": 8}], "header": {"packet_number": 160, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073428.718}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073428.718}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073428.718}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 33174, "stream_id": 8}], "header": {"packet_number": 161, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073428.718}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073428.718}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073428.718}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 34602, "stream_id": 8}], "header": {"packet_number": 162, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073428.718}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073428.718}, {"data": {"frames": [{"ack_delay": 14.999999999417923, "acked_ranges": [[134, 162]], "frame_type": "ack"}], "header": {"packet_number": 29, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836073429.717}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836073429.717}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073431.978}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 36030, "stream_id": 8}], "header": {"packet_number": 163, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073431.978}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073431.978}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073431.978}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 652, "offset": 37458, "stream_id": 8}, {"fin": false, "frame_type": "stream", "length": 768, "offset": 38110, "stream_id": 8}], "header": {"packet_number": 164, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073431.978}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073431.978}, {"data": {"frame": {"frame_type": "data"}, "length": 4808, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073431.978}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073432.9875}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 38878, "stream_id": 8}], "header": {"packet_number": 165, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073432.9875}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073432.9875}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073433.2983}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 40306, "stream_id": 8}], "header": {"packet_number": 166, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073433.2983}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073433.2983}, {"data": {"count": 1, "raw": [{"length": 1229, "payload_length": 1221}]}, "name": "transport:datagrams_received", "time": 1750836073433.2983}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1187, "offset": 41734, "stream_id": 8}], "header": {"packet_number": 167, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1221}}, "name": "transport:packet_received", "time": 1750836073433.2983}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073433.2983}, {"data": {"frames": [{"ack_delay": 15.999999995983671, "acked_ranges": [[134, 167]], "frame_type": "ack"}], "header": {"packet_number": 30, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 46}}, "name": "transport:packet_sent", "time": 1750836073446.825}, {"data": {"count": 1, "raw": [{"length": 54, "payload_length": 46}]}, "name": "transport:datagrams_sent", "time": 1750836073446.825}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073568.8398}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 42921, "stream_id": 8}], "header": {"packet_number": 168, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073568.8398}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073568.8398}, {"data": {"frame": {"frame_type": "data"}, "length": 13134, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073568.8398}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073568.8398}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 44349, "stream_id": 8}], "header": {"packet_number": 169, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073568.8398}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073568.8398}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073569.8386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 45777, "stream_id": 8}], "header": {"packet_number": 170, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073569.8386}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073569.8386}, {"data": {"count": 1, "raw": [{"length": 327, "payload_length": 319}]}, "name": "transport:datagrams_received", "time": 1750836073569.8386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 285, "offset": 55773, "stream_id": 8}], "header": {"packet_number": 177, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 319}}, "name": "transport:packet_received", "time": 1750836073569.8386}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073569.8386}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073569.8386}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 47205, "stream_id": 8}], "header": {"packet_number": 171, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073570.8345}, {"data": {"frames": [{"ack_delay": 15.999999995983671, "acked_ranges": [[134, 171], [177, 177]], "frame_type": "ack"}], "header": {"packet_number": 31, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 48}}, "name": "transport:packet_sent", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 56, "payload_length": 48}]}, "name": "transport:datagrams_sent", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073570.8345}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 48633, "stream_id": 8}], "header": {"packet_number": 172, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073570.8345}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 50061, "stream_id": 8}], "header": {"packet_number": 173, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073570.8345}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 51489, "stream_id": 8}], "header": {"packet_number": 174, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073570.8345}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 52917, "stream_id": 8}], "header": {"packet_number": 175, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073570.8345}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 54345, "stream_id": 8}], "header": {"packet_number": 176, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 1151, "payload_length": 1143}]}, "name": "transport:datagrams_received", "time": 1750836073570.8345}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1109, "offset": 56058, "stream_id": 8}], "header": {"packet_number": 178, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1143}}, "name": "transport:packet_received", "time": 1750836073570.8345}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073570.8345}, {"data": {"frame": {"frame_type": "data"}, "length": 1106, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073570.8345}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073571.9343}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 57167, "stream_id": 8}], "header": {"packet_number": 179, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073571.9343}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073571.9343}, {"data": {"frame": {"frame_type": "data"}, "length": 11674, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073571.9343}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073571.9343}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 58595, "stream_id": 8}], "header": {"packet_number": 180, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073571.9343}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073571.9343}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073571.9343}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 60023, "stream_id": 8}], "header": {"packet_number": 181, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073571.9343}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073571.9343}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073571.9343}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 61451, "stream_id": 8}], "header": {"packet_number": 182, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073571.9343}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073571.9343}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073572.9421}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 62879, "stream_id": 8}], "header": {"packet_number": 183, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073572.9421}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073572.9421}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073572.9421}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 64307, "stream_id": 8}], "header": {"packet_number": 184, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073572.9421}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073572.9421}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073572.9421}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 65735, "stream_id": 8}], "header": {"packet_number": 185, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073572.9421}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073572.9421}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073572.9421}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 67163, "stream_id": 8}], "header": {"packet_number": 186, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073572.9421}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073572.9421}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073573.9424}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 253, "offset": 68591, "stream_id": 8}, {"fin": false, "frame_type": "stream", "length": 1167, "offset": 68844, "stream_id": 8}], "header": {"packet_number": 187, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073573.9424}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073573.9424}, {"data": {"frame": {"frame_type": "data"}, "length": 4704, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073573.9424}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073573.9424}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 70011, "stream_id": 8}], "header": {"packet_number": 188, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073573.9424}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073573.9424}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073574.942}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 71439, "stream_id": 8}], "header": {"packet_number": 189, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073574.942}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073574.942}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073579.942}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 684, "offset": 72867, "stream_id": 8}, {"fin": false, "frame_type": "stream", "length": 736, "offset": 73551, "stream_id": 8}], "header": {"packet_number": 190, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073579.942}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073579.942}, {"data": {"frame": {"frame_type": "data"}, "length": 2571, "stream_id": 8}, "name": "http:frame_parsed", "time": 1750836073579.942}, {"data": {"count": 1, "raw": [{"length": 1470, "payload_length": 1462}]}, "name": "transport:datagrams_received", "time": 1750836073579.942}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 1428, "offset": 74287, "stream_id": 8}], "header": {"packet_number": 191, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 1462}}, "name": "transport:packet_received", "time": 1750836073579.942}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073579.942}, {"data": {"count": 1, "raw": [{"length": 459, "payload_length": 451}]}, "name": "transport:datagrams_received", "time": 1750836073580.9387}, {"data": {"frames": [{"fin": false, "frame_type": "stream", "length": 410, "offset": 75715, "stream_id": 8}, {"fin": true, "frame_type": "stream", "length": 0, "offset": 76125, "stream_id": 8}], "header": {"packet_number": 192, "packet_type": "1RTT", "dcid": "f6fdd4a209d66549", "scid": ""}, "raw": {"length": 451}}, "name": "transport:packet_received", "time": 1750836073580.9387}, {"data": {"state": true}, "name": "connectivity:spin_bit_updated", "time": 1750836073580.9387}, {"data": {"frames": [{"error_code": 0, "error_space": "application", "frame_type": "connection_close", "raw_error_code": 0, "reason": ""}], "header": {"packet_number": 32, "packet_type": "1RTT", "scid": "", "dcid": "****************************************"}, "raw": {"length": 42}}, "name": "transport:packet_sent", "time": 1750836073586.578}, {"data": {"count": 1, "raw": [{"length": 50, "payload_length": 42}]}, "name": "transport:datagrams_sent", "time": 1750836073586.578}, {"data": {"key_type": "server_1rtt_secret", "trigger": "tls"}, "name": "security:key_retired", "time": 1750836073864.4233}, {"data": {"key_type": "client_1rtt_secret", "trigger": "tls"}, "name": "security:key_retired", "time": 1750836073864.4233}, {"data": {"cwnd": 13672, "bytes_in_flight": 0}, "name": "recovery:metrics_updated", "time": 1750836073864.4233}], "vantage_point": {"name": "aioquic", "type": "client"}}]}