#!/usr/bin/env python3
"""
HTTP/3客户端，支持QPACK动态表调试功能

这个客户端基于aioquic项目，专门用于测试和调试QPACK动态表的压缩功能。
它能够发送包含大型头部字段的HTTP/3请求，并显示详细的QPACK操作信息。
"""

import argparse
import asyncio
import logging
import ssl
import sys
import time
from collections import deque
from typing import Dict, List, Optional, Deque
from urllib.parse import urlparse
from pathlib import Path

# 使用已安装的aioquic
# sys.path.insert(0, str(Path(__file__).parent / "src"))

import aioquic
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h3.connection import H3_ALPN, H3Connection
from aioquic.h3.events import DataReceived, H3Event, HeadersReceived
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger

# QPACK静态表（RFC 9204 Appendix A）- 部分常用条目
QPACK_STATIC_TABLE = {
    0: (":authority", ""),
    1: (":path", "/"),
    2: ("age", "0"),
    3: ("content-disposition", ""),
    4: ("content-length", "0"),
    5: ("cookie", ""),
    6: ("date", ""),
    7: ("etag", ""),
    8: ("if-modified-since", ""),
    9: ("if-none-match", ""),
    10: ("last-modified", ""),
    11: ("link", ""),
    12: ("location", ""),
    13: ("referer", ""),
    14: ("set-cookie", ""),
    15: (":method", "CONNECT"),
    16: (":method", "DELETE"),
    17: (":method", "GET"),
    18: (":method", "HEAD"),
    19: (":method", "OPTIONS"),
    20: (":method", "POST"),
    21: (":method", "PUT"),
    22: (":scheme", "http"),
    23: (":scheme", "https"),
    24: (":status", "103"),
    25: (":status", "200"),
    26: (":status", "304"),
    27: (":status", "404"),
    28: (":status", "503"),
    29: ("accept", "*/*"),
    30: ("accept", "application/dns-message"),
    31: ("accept-encoding", "gzip, deflate, br"),
    32: ("accept-ranges", "bytes"),
    33: ("access-control-allow-headers", "cache-control"),
    34: ("access-control-allow-headers", "content-type"),
    35: ("access-control-allow-origin", "*"),
    36: ("cache-control", "max-age=0"),
    37: ("cache-control", "max-age=2592000"),
    38: ("cache-control", "max-age=604800"),
    39: ("cache-control", "no-cache"),
    40: ("cache-control", "no-store"),
    41: ("cache-control", "public, max-age=31536000"),
    42: ("content-encoding", "br"),
    43: ("content-encoding", "gzip"),
    44: ("content-type", "application/dns-message"),
    45: ("content-type", "application/javascript"),
    46: ("content-type", "application/json"),
    47: ("content-type", "application/x-www-form-urlencoded"),
    48: ("content-type", "image/gif"),
    49: ("content-type", "image/jpeg"),
    50: ("content-type", "text/css"),
    51: ("content-type", "text/html; charset=utf-8"),
    52: ("content-type", "text/plain"),
    53: ("content-type", "text/plain;charset=utf-8"),
    54: ("range", "bytes=0-"),
    55: ("strict-transport-security", "max-age=31536000"),
    56: ("vary", "accept-encoding"),
    57: ("x-content-type-options", "nosniff"),
    58: ("x-xss-protection", "1; mode=block"),
    59: (":status", "100"),
    60: (":status", "204"),
    61: (":status", "206"),
    62: (":status", "300"),
    63: (":status", "400"),
    64: (":status", "403"),
    65: (":status", "421"),
    66: (":status", "425"),
    67: (":status", "500"),
    68: ("accept-language", ""),
    69: ("authorization", ""),
    70: ("content-security-policy", "script-src 'none'; object-src 'none'; base-uri 'none'"),
    71: ("early-data", "1"),
    72: ("expect-ct", ""),
    73: ("forwarded", ""),
    74: ("if-range", ""),
    75: ("origin", ""),
    76: ("purpose", "prefetch"),
    77: ("server", ""),
    78: ("timing-allow-origin", "*"),
    79: ("upgrade-insecure-requests", "1"),
    80: ("user-agent", ""),
    81: ("x-forwarded-for", ""),
    82: ("x-frame-options", "deny"),
    83: ("x-frame-options", "sameorigin"),
}

def get_qpack_table_entry(index: int, is_static: bool = True) -> str:
    """获取QPACK表条目的可读描述"""
    if is_static and index in QPACK_STATIC_TABLE:
        name, value = QPACK_STATIC_TABLE[index]
        if value:
            return f'"{name}: {value}"'
        else:
            return f'"{name}: <value>"'
    elif not is_static:
        return f"动态表条目[{index}]"
    else:
        return f"未知静态表条目[{index}]"

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("qpack_debug_client")

# 大型头部字段用于测试QPACK动态表
LARGE_COOKIE = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
LARGE_AUTH_TOKEN = "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"

USER_AGENT = f"qpack-debug-client/aioquic-{aioquic.__version__}"


class QpackDebugStats:
    """QPACK调试统计信息"""
    
    def __init__(self):
        self.request_count = 0
        self.total_header_bytes_before = 0
        self.total_header_bytes_after = 0
        self.dynamic_table_entries = []
        
    def add_request(self, headers_before: int, headers_after: int):
        """统计QPACK压缩效果
        
        headers_before: 压缩前的头部字节数
        headers_after: 压缩后的头部字节数
        """
        self.request_count += 1
        self.total_header_bytes_before += headers_before
        self.total_header_bytes_after += headers_after
        
    def get_compression_ratio(self) -> float:
        if self.total_header_bytes_before == 0:
            return 0.0
        return (1 - self.total_header_bytes_after / self.total_header_bytes_before) * 100
        
    def get_bytes_saved(self) -> int:
        return self.total_header_bytes_before - self.total_header_bytes_after


class QpackDebugClient(QuicConnectionProtocol):
    """支持QPACK调试的HTTP/3客户端"""

    def __init__(self, *args, **kwargs):

        super().__init__(*args, **kwargs)
        # HTTP/3连接对象
        self._http: Optional[H3Connection] = None
        # 保存每个流的HTTP/3事件
        self._request_events: Dict[int, Deque[H3Event]] = {}
        # 保存每个流的事件等待器
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        # QPACK调试统计信息
        self._stats = QpackDebugStats()

        # 事件驱动相关
        self._settings_received = False
        self._settings_waiter: Optional[asyncio.Future] = None
        self._pending_requests = []  # 等待SETTINGS的请求队列

        # 初始化HTTP/3连接，使用更大的动态表容量来促进优化
        if self._quic.configuration.alpn_protocols[0] in H3_ALPN:
            self._http = H3Connection(self._quic)
            # 修改动态表参数以促进更早的优化
            self._http._max_table_capacity = 16384  # 增加到16KB
            self._http._blocked_streams = 32  # 增加阻塞流数量
            logger.info(f"[QPACK优化] 设置动态表容量: {self._http._max_table_capacity} bytes")
            logger.info(f"[QPACK优化] 设置阻塞流数量: {self._http._blocked_streams}")

    def quic_event_received(self, event: QuicEvent) -> None:
        """处理QUIC事件"""
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)

    def http_event_received(self, event: H3Event) -> None:
        """处理HTTP/3事件"""
        # 检查SETTINGS是否已接收（通过H3Connection的内部状态）
        if not self._settings_received and self._http._settings_received:
            self._settings_received = True
            logger.info(f"[事件驱动] ✓ 检测到SETTINGS帧已接收，开始处理等待的请求")

            # 通知等待SETTINGS的协程
            if self._settings_waiter and not self._settings_waiter.done():
                self._settings_waiter.set_result(True)

        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[stream_id].append(event)

                # 如果流结束，完成Future
                if event.stream_ended and stream_id in self._request_waiter:
                    waiter = self._request_waiter.pop(stream_id)
                    waiter.set_result(self._request_events.pop(stream_id))

    async def wait_for_settings(self, timeout: float = 5.0) -> bool:
        """等待SETTINGS帧接收"""
        if self._settings_received:
            return True

        if self._settings_waiter is None or self._settings_waiter.done():
            self._settings_waiter = asyncio.Future()

        try:
            await asyncio.wait_for(self._settings_waiter, timeout=timeout)
            return True
        except asyncio.TimeoutError:
            logger.warning(f"[事件驱动] ⚠ 等待SETTINGS帧超时 ({timeout}s)")
            return False

    async def send_request(self, url: str, headers: Optional[Dict[str, str]] = None) -> Deque[H3Event]:
        """发送HTTP/3请求"""
        if self._http is None:
            raise RuntimeError("HTTP/3连接未初始化")

        # 解析URL
        parsed = urlparse(url)
        authority = parsed.netloc
        path = parsed.path or "/"
        if parsed.query:
            path += "?" + parsed.query

        # 构建请求头部
        request_headers = [
            (b":method", b"GET"),
            (b":scheme", b"https"),
            (b":authority", authority.encode()),
            (b":path", path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]

        # 添加自定义头部
        if headers:
            for name, value in headers.items():
                request_headers.append((name.encode(), value.encode()))

        # 记录请求前的QPACK状态
        encoder_bytes_before = self._http._encoder_bytes_sent
        decoder_bytes_before = self._http._decoder_bytes_sent

        # 估算头部大小（用于统计）,+4 处理长度前缀
        header_size_estimate = sum(len(name) + len(value) + 4 for name, value in request_headers)

        logger.info(f"[请求 {self._stats.request_count + 1}] 发送请求到 {url}")
        logger.info(f"[QPACK] 预估头部大小: {header_size_estimate} bytes")

        # 发送请求 - 使用自定义方法来捕获真实的QPACK数据
        stream_id = self._quic.get_next_available_stream_id()
        encoder_data, frame_data = self._encode_headers_with_debug(stream_id, request_headers)

        # 发送编码后的头部
        from aioquic.h3.connection import FrameType, encode_frame
        self._quic.send_stream_data(
            stream_id, encode_frame(FrameType.HEADERS, frame_data), True
        )

        # 设置等待器
        waiter = self._loop.create_future()
        self._request_waiter[stream_id] = waiter
        self._request_events[stream_id] = deque()

        # 传输数据
        self.transmit()

        # 等待响应
        events = await asyncio.shield(waiter)

        # 记录请求后的QPACK状态
        encoder_bytes_after = self._http._encoder_bytes_sent
        decoder_bytes_after = self._http._decoder_bytes_sent

        # 计算实际传输的头部字节数
        actual_header_bytes = len(frame_data)

        # 更新统计信息
        self._stats.add_request(header_size_estimate, actual_header_bytes)

        # 输出QPACK调试信息 - 传递真实的编码器数据
        self._print_qpack_debug_info(
            request_headers,
            header_size_estimate,
            actual_header_bytes,
            encoder_data,
            frame_data,
            encoder_bytes_after - encoder_bytes_before,
            decoder_bytes_after - decoder_bytes_before
        )

        return events

    def _encode_headers_with_debug(self, stream_id: int, headers) -> tuple:
        """编码头部并返回调试信息"""
        # 检查是否是第1次请求，如果是则记录优化设置
        if self._stats.request_count == 0:
            logger.info(f"[QPACK优化] 第1次请求，使用优化的动态表设置")

        # 直接调用编码器获取真实数据
        encoder_data, frame_data = self._http._encoder.encode(stream_id, headers)

        # 发送编码器数据到编码器流
        if encoder_data:
            self._http._encoder_bytes_sent += len(encoder_data)
            self._quic.send_stream_data(self._http._local_encoder_stream_id, encoder_data)

        return encoder_data, frame_data



    def _force_dynamic_table_creation(self, headers):
        """强制创建动态表条目的备用策略"""
        logger.info(f"[QPACK优化] 启动备用策略：强制动态表创建")

        # 创建大量不同的头部来强制编码器建立动态表
        base_stream_id = 888

        for i in range(20):  # 创建20个不同的请求
            try:
                # 创建变化的头部
                varied_headers = list(headers)

                # 添加变化的自定义头部
                varied_headers.append((b"x-force-dynamic", f"value-{i}".encode()))
                varied_headers.append((b"x-iteration", str(i).encode()))

                # 修改现有头部的值
                for j, (name, value) in enumerate(varied_headers):
                    if name == b"cookie":
                        # 修改cookie值来创建变化
                        varied_headers[j] = (name, value + f"-iter{i}".encode())
                    elif name == b"x-custom-auth-token":
                        # 修改auth token值
                        varied_headers[j] = (name, value + f"-v{i}".encode())

                # 编码这个变化的头部
                encoder_data, frame_data = self._http._encoder.encode(base_stream_id + i, varied_headers)

                if encoder_data:
                    self._http._encoder_bytes_sent += len(encoder_data)
                    self._quic.send_stream_data(self._http._local_encoder_stream_id, encoder_data)
                    logger.info(f"[QPACK优化] 备用策略 {i+1}: 生成 {len(encoder_data)} 字节编码器数据")

            except Exception as e:
                logger.warning(f"[QPACK优化] 备用策略编码 {i+1} 失败: {e}")
                continue

        logger.info(f"[QPACK优化] 备用策略完成")

    def _print_qpack_debug_info(self, headers, estimated_size: int, actual_size: int,
                               encoder_data: bytes, frame_data: bytes,
                               encoder_delta: int, decoder_delta: int):
        """打印真实的QPACK调试信息"""
        request_num = self._stats.request_count

        logger.info(f"[QPACK 真实数据] 编码器字节增量: {encoder_delta}")
        logger.info(f"[QPACK 真实数据] 解码器字节增量: {decoder_delta}")
        logger.info(f"[QPACK 真实数据] 编码器累计字节: {self._http._encoder_bytes_sent}")
        logger.info(f"[QPACK 真实数据] 解码器累计字节: {self._http._decoder_bytes_sent}")

        # 显示真实的编码器数据
        if encoder_data:
            logger.info(f"[QPACK 编码器流] 数据长度: {len(encoder_data)} bytes")
            logger.info(f"[QPACK 编码器流] 十六进制数据: {encoder_data.hex()}")
            self._analyze_encoder_data(encoder_data)
        else:
            logger.info(f"[QPACK 编码器流] 无编码器数据 - 可能使用静态表或已有动态表条目")

        # 显示真实的帧数据
        logger.info(f"[QPACK 头部帧] 压缩后大小: {len(frame_data)} bytes")
        logger.info(f"[QPACK 头部帧] 十六进制数据: {frame_data.hex()}")

        # 分析头部帧数据
        self._analyze_frame_data(frame_data, headers)

        # 计算真实的压缩比例
        if estimated_size > 0:
            compression_ratio = (1 - actual_size / estimated_size) * 100
            bytes_saved = estimated_size - actual_size
            logger.info(f"[真实压缩统计] 原始大小: {estimated_size} bytes")
            logger.info(f"[真实压缩统计] 压缩后大小: {actual_size} bytes")
            logger.info(f"[真实压缩统计] 节省: {bytes_saved} bytes ({compression_ratio:.1f}%)")

        # 总体统计
        total_compression = self._stats.get_compression_ratio()
        total_saved = self._stats.get_bytes_saved()
        logger.info(f"[总体统计] 请求数: {request_num}")
        logger.info(f"[总体统计] 累计节省: {total_saved} bytes ({total_compression:.1f}%)")

        # 动态表效果分析
        if request_num > 1:
            if encoder_delta == 0:
                logger.info(f"[动态表效果] ✓ 完全使用动态表引用，无需发送新条目")
                logger.info(f"[动态表效果] 这表明之前建立的动态表条目被有效重用")
            else:
                logger.info(f"[动态表效果] ⚠ 仍在发送编码器数据，可能添加新条目")
                logger.info(f"[动态表效果] 编码器正在优化动态表以提高后续压缩效果")

        logger.info("=" * 60)

    def _analyze_encoder_data(self, encoder_data: bytes):
        """分析编码器流数据"""
        if not encoder_data:
            return

        logger.info(f"[编码器数据分析] 正在分析 {len(encoder_data)} 字节的编码器数据...")

        # 简单的QPACK编码器指令分析
        # 根据QPACK规范，编码器指令的第一个字节指示指令类型
        i = 0
        instruction_count = 0

        while i < len(encoder_data):
            instruction_count += 1
            byte = encoder_data[i]

            if byte & 0x80:  # 10xxxxxx - Insert With Name Reference
                logger.info(f"[编码器指令 {instruction_count}] 插入带名称引用 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 向动态表添加新条目，引用现有名称")
                # 跳过这个指令的其余部分（简化处理）
                i += 1
                while i < len(encoder_data) and encoder_data[i] & 0x80 == 0:
                    i += 1

            elif byte & 0x40:  # 01xxxxxx - Insert With Literal Name
                logger.info(f"[编码器指令 {instruction_count}] 插入带字面量名称 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 向动态表添加全新的头部字段")
                i += 1

            elif byte & 0x20:  # 001xxxxx - Duplicate
                index = byte & 0x1f
                logger.info(f"[编码器指令 {instruction_count}] 复制条目 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 复制动态表索引 {index} 的条目")
                i += 1

            else:  # 00xxxxxx - Set Dynamic Table Capacity
                capacity = byte & 0x3f
                logger.info(f"[编码器指令 {instruction_count}] 设置动态表容量 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 设置容量为 {capacity}")
                i += 1

        logger.info(f"[编码器数据分析] 共发现 {instruction_count} 个编码器指令")
        if instruction_count > 0:
            logger.info(f"[编码器数据分析] ✓ 检测到动态表操作！")
            logger.info(f"[QPACK机制说明] 编码器将重复数据分解为多个动态表条目")
            logger.info(f"[QPACK机制说明] 这样可以在后续请求中通过索引引用实现高压缩率")

    def _analyze_frame_data(self, frame_data: bytes, headers):
        """分析头部帧数据"""
        if not frame_data:
            return

        logger.info(f"[头部帧分析] 正在分析 {len(frame_data)} 字节的头部数据...")

        # 分析QPACK头部块
        # 前两个字节通常是Required Insert Count和Base
        if len(frame_data) >= 2:
            required_insert_count = frame_data[0]
            base = frame_data[1] if len(frame_data) > 1 else 0
            logger.info(f"[头部帧分析] Required Insert Count: {required_insert_count}")
            logger.info(f"[头部帧分析] Base: {base}")

            if required_insert_count > 0:
                logger.info(f"[头部帧分析] ✓ 依赖动态表条目 (需要 {required_insert_count} 个插入)")
            else:
                logger.info(f"[头部帧分析] → 不依赖动态表条目")

        # 分析头部字段表示
        i = 2  # 跳过前缀
        field_count = 0

        while i < len(frame_data):
            field_count += 1
            byte = frame_data[i]

            if byte & 0x80:  # 1xxxxxxx - Indexed Header Field
                index = byte & 0x7f
                table_entry = get_qpack_table_entry(index, is_static=(index < 99))
                logger.info(f"[头部字段 {field_count}] 索引头部字段 (0x{byte:02x})")
                logger.info(f"[头部字段 {field_count}] → 使用索引 {index} = {table_entry}")
                i += 1

            elif byte & 0x40:  # 01xxxxxx - Literal Header Field with Name Reference
                index = byte & 0x3f
                table_entry = get_qpack_table_entry(index, is_static=(index < 99))
                logger.info(f"[头部字段 {field_count}] 字面量头部字段带名称引用 (0x{byte:02x})")
                logger.info(f"[头部字段 {field_count}] → 引用索引 {index} 的名称 = {table_entry}")
                i += 1
                # 跳过值部分（简化）
                while i < len(frame_data) and frame_data[i] & 0x80 == 0:
                    i += 1

            elif byte & 0x20:  # 001xxxxx - Literal Header Field with Literal Name
                logger.info(f"[头部字段 {field_count}] 字面量头部字段带字面量名称 (0x{byte:02x})")
                logger.info(f"[头部字段 {field_count}] → 完全字面量字段")
                i += 1

            else:  # 其他模式
                logger.info(f"[头部字段 {field_count}] 其他编码模式 (0x{byte:02x})")
                i += 1

        logger.info(f"[头部帧分析] 共发现 {field_count} 个头部字段编码")

        # 检查是否有动态表使用的证据
        dynamic_table_used = any(b & 0x80 and (b & 0x7f) >= 62 for b in frame_data[2:])
        if dynamic_table_used:
            logger.info(f"[头部帧分析] ✓ 检测到动态表索引使用！")
        else:
            logger.info(f"[头部帧分析] → 未检测到明显的动态表使用")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HTTP/3 QPACK调试客户端")
    parser.add_argument("--host", default="aliyun.hawks.top", help="服务器主机名")
    parser.add_argument("--port", type=int, default=443, help="服务器端口")
    parser.add_argument("--path", default="/", help="请求路径")
    parser.add_argument("--requests", type=int, default=3, help="发送请求次数")
    parser.add_argument("--insecure", action="store_true", help="跳过证书验证")
    parser.add_argument("--quic-log", help="QUIC日志文件路径夹")
    parser.add_argument("--secrets-log", help="SSL密钥日志文件路径（用于Wireshark解密）")
    
    args = parser.parse_args() 
    
    # 配置QUIC连接
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=H3_ALPN,
        max_datagram_frame_size=65536,
    )
    
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
        
    if args.quic_log:
        # 确保QUIC日志目录存在
        quic_log_path = Path(args.quic_log)
        quic_log_path.mkdir(parents=True, exist_ok=True)
        configuration.quic_logger = QuicFileLogger(args.quic_log)

    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
        
    # 构建URL
    url = f"https://{args.host}:{args.port}{args.path}"
    
    logger.info(f"连接到 {args.host}:{args.port}")
    logger.info(f"将发送 {args.requests} 个请求到 {url}")
    logger.info(f"大型Cookie头部: {len(LARGE_COOKIE)} bytes")
    logger.info(f"大型Auth-Token头部: {len(LARGE_AUTH_TOKEN)} bytes")
    if args.quic_log:
        logger.info(f"QUIC日志将保存到: {args.quic_log}")
    if args.secrets_log:
        logger.info(f"SSL密钥日志将保存到文件夹: {args.secrets_log}")
        logger.info("💡 提示: 在Wireshark中设置 Edit -> Preferences -> Protocols -> TLS -> (Pre)-Master-Secret log filename")
    logger.info("=" * 60)
    
    # 建立连接
    async with connect(
        args.host,
        args.port,
        configuration=configuration,
        create_protocol=QpackDebugClient,
    ) as client:
        client = client  # type: QpackDebugClient
        
        # 事件驱动的请求发送策略
        logger.info(f"[事件驱动] 开始发送第1个请求...")

        # 发送第1个请求（触发SETTINGS协商）
        headers_1 = {
            "cookie": LARGE_COOKIE,
            "x-custom-auth-token": LARGE_AUTH_TOKEN,
            "x-request-id": f"req-1-{int(time.time())}",
        }

        try:
            events = await client.send_request(url, headers_1)

            # 处理第1个响应
            for event in events:
                if isinstance(event, HeadersReceived):
                    logger.info(f"[响应] 状态码: {dict(event.headers).get(b':status', b'unknown').decode()}")

            # 等待SETTINGS帧接收
            logger.info(f"[事件驱动] 等待服务器SETTINGS帧...")
            settings_received = await client.wait_for_settings(timeout=10.0)

            if settings_received:
                logger.info(f"[事件驱动] ✓ SETTINGS帧已接收，动态表设置已协商完成")
                logger.info(f"[事件驱动] 现在发送后续请求，这些请求将能够利用已建立的动态表")

                # 发送剩余请求，从第2次开始增加延迟
                for i in range(1, args.requests):
                    # 从第2次请求开始增加延迟，给编码器更多学习时间
                    if i >= 1:  # 第2次及以后的请求
                        delay = 1.0 + (i - 1) * 0.5  # 1.0s, 1.5s, 2.0s, 2.5s...
                        logger.info(f"[延迟策略] 第{i+1}次请求前等待 {delay}s，给编码器更多学习时间")
                        await asyncio.sleep(delay)

                    headers = {
                        "cookie": LARGE_COOKIE,
                        "x-custom-auth-token": LARGE_AUTH_TOKEN,
                        "x-request-id": f"req-{i+1}-{int(time.time())}",
                    }

                    try:
                        events = await client.send_request(url, headers)

                        # 处理响应
                        for event in events:
                            if isinstance(event, HeadersReceived):
                                logger.info(f"[响应] 状态码: {dict(event.headers).get(b':status', b'unknown').decode()}")

                    except Exception as e:
                        logger.error(f"请求 {i+1} 失败: {e}")
            else:
                logger.warning(f"[事件驱动] ⚠ SETTINGS帧接收超时，继续发送剩余请求")
                # 即使没有收到SETTINGS，也继续发送剩余请求
                for i in range(1, args.requests):
                    headers = {
                        "cookie": LARGE_COOKIE,
                        "x-custom-auth-token": LARGE_AUTH_TOKEN,
                        "x-request-id": f"req-{i+1}-{int(time.time())}",
                    }

                    try:
                        events = await client.send_request(url, headers)

                        # 处理响应
                        for event in events:
                            if isinstance(event, HeadersReceived):
                                logger.info(f"[响应] 状态码: {dict(event.headers).get(b':status', b'unknown').decode()}")

                        # 在请求之间稍作延迟
                        if i < args.requests - 1:
                            await asyncio.sleep(0.5)

                    except Exception as e:
                        logger.error(f"请求 {i+1} 失败: {e}")

        except Exception as e:
            logger.error(f"第1个请求失败: {e}")
                
    logger.info("所有请求完成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断")
    except Exception as e:
        logger.error(f"程序错误: {e}")


#  使用方式
# python qpack_debug_client.py --host aliyun.hawks.top --requests 4 --insecure --quic-log qpack_debug