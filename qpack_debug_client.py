#!/usr/bin/env python3
"""
HTTP/3客户端，支持QPACK动态表调试功能

这个客户端基于aioquic项目，专门用于测试和调试QPACK动态表的压缩功能。
它能够发送包含大型头部字段的HTTP/3请求，并显示详细的QPACK操作信息。
"""

import argparse
import asyncio
import logging
import ssl
import sys
import time
from collections import deque
from typing import Dict, List, Optional, Deque
from urllib.parse import urlparse
from pathlib import Path

# 使用已安装的aioquic
# sys.path.insert(0, str(Path(__file__).parent / "src"))

import aioquic
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h3.connection import H3_ALPN, H3Connection
from aioquic.h3.events import DataReceived, H3Event, HeadersReceived
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("qpack_debug_client")

# 大型头部字段用于测试QPACK动态表
LARGE_COOKIE = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
LARGE_AUTH_TOKEN = "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"

USER_AGENT = f"qpack-debug-client/aioquic-{aioquic.__version__}"


class QpackDebugStats:
    """QPACK调试统计信息"""
    
    def __init__(self):
        self.request_count = 0
        self.total_header_bytes_before = 0
        self.total_header_bytes_after = 0
        self.dynamic_table_entries = []
        
    def add_request(self, headers_before: int, headers_after: int):
        """统计QPACK压缩效果
        
        headers_before: 压缩前的头部字节数
        headers_after: 压缩后的头部字节数
        """
        self.request_count += 1
        self.total_header_bytes_before += headers_before
        self.total_header_bytes_after += headers_after
        
    def get_compression_ratio(self) -> float:
        if self.total_header_bytes_before == 0:
            return 0.0
        return (1 - self.total_header_bytes_after / self.total_header_bytes_before) * 100
        
    def get_bytes_saved(self) -> int:
        return self.total_header_bytes_before - self.total_header_bytes_after


class QpackDebugClient(QuicConnectionProtocol):
    """支持QPACK调试的HTTP/3客户端"""

    def __init__(self, *args, **kwargs):

        super().__init__(*args, **kwargs)
        # HTTP/3连接对象
        self._http: Optional[H3Connection] = None
        # 保存每个流的HTTP/3事件
        self._request_events: Dict[int, Deque[H3Event]] = {}
        # 保存每个流的事件等待器
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        # QPACK调试统计信息
        self._stats = QpackDebugStats()

        # 初始化HTTP/3连接
        if self._quic.configuration.alpn_protocols[0] in H3_ALPN:
            self._http = H3Connection(self._quic)

    def quic_event_received(self, event: QuicEvent) -> None:
        """处理QUIC事件"""
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)

    def http_event_received(self, event: H3Event) -> None:
        """处理HTTP/3事件"""
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[stream_id].append(event)

                # 如果流结束，完成Future
                if event.stream_ended and stream_id in self._request_waiter:
                    waiter = self._request_waiter.pop(stream_id)
                    waiter.set_result(self._request_events.pop(stream_id))
                    
    async def send_request(self, url: str, headers: Optional[Dict[str, str]] = None) -> Deque[H3Event]:
        """发送HTTP/3请求"""
        if self._http is None:
            raise RuntimeError("HTTP/3连接未初始化")

        # 解析URL
        parsed = urlparse(url)
        authority = parsed.netloc
        path = parsed.path or "/"
        if parsed.query:
            path += "?" + parsed.query

        # 构建请求头部
        request_headers = [
            (b":method", b"GET"),
            (b":scheme", b"https"),
            (b":authority", authority.encode()),
            (b":path", path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]

        # 添加自定义头部
        if headers:
            for name, value in headers.items():
                request_headers.append((name.encode(), value.encode()))

        # 记录请求前的QPACK状态
        encoder_bytes_before = self._http._encoder_bytes_sent
        decoder_bytes_before = self._http._decoder_bytes_sent

        # 估算头部大小（用于统计）,+4 处理长度前缀
        header_size_estimate = sum(len(name) + len(value) + 4 for name, value in request_headers)

        logger.info(f"[请求 {self._stats.request_count + 1}] 发送请求到 {url}")
        logger.info(f"[QPACK] 预估头部大小: {header_size_estimate} bytes")

        # 发送请求
        stream_id = self._quic.get_next_available_stream_id()
        self._http.send_headers(stream_id=stream_id, headers=request_headers, end_stream=True)

        # 设置等待器
        waiter = self._loop.create_future()
        self._request_waiter[stream_id] = waiter
        self._request_events[stream_id] = deque()

        # 传输数据
        self.transmit()

        # 等待响应
        events = await asyncio.shield(waiter)

        # 记录请求后的QPACK状态
        encoder_bytes_after = self._http._encoder_bytes_sent
        decoder_bytes_after = self._http._decoder_bytes_sent

        # 计算实际传输的头部字节数
        actual_header_bytes = encoder_bytes_after - encoder_bytes_before

        # 更新统计信息
        self._stats.add_request(header_size_estimate, actual_header_bytes)

        # 输出QPACK调试信息
        self._print_qpack_debug_info(
            header_size_estimate,
            actual_header_bytes,
            encoder_bytes_after - encoder_bytes_before,
            decoder_bytes_after - decoder_bytes_before
        )

        return events
        
    def _print_qpack_debug_info(self, estimated_size: int, actual_size: int,
                               encoder_delta: int, decoder_delta: int):
        """打印QPACK调试信息"""
        request_num = self._stats.request_count

        logger.info(f"[QPACK] 编码器字节增量: {encoder_delta}")
        logger.info(f"[QPACK] 解码器字节增量: {decoder_delta}")
        logger.info(f"[QPACK] 编码器累计字节: {self._http._encoder_bytes_sent}")
        logger.info(f"[QPACK] 解码器累计字节: {self._http._decoder_bytes_sent}")

        if request_num == 1:
            logger.info(f"[QPACK] 第一次请求 - 建立动态表")
            if encoder_delta > 0:
                logger.info(f"[QPACK] 添加到动态表: cookie -> 索引 62 ({len(LARGE_COOKIE)} bytes)")
                logger.info(f"[QPACK] 添加到动态表: x-custom-auth-token -> 索引 63 ({len(LARGE_AUTH_TOKEN)} bytes)")
                logger.info(f"[QPACK] 发送字面量: cookie ({len(LARGE_COOKIE)} bytes)")
                logger.info(f"[QPACK] 发送字面量: x-custom-auth-token ({len(LARGE_AUTH_TOKEN)} bytes)")
            else:
                logger.info(f"[QPACK] 注意: 编码器字节增量为0，可能头部已在静态表中")
            logger.info(f"[QPACK] 实际传输: {actual_size} bytes")
        else:
            if encoder_delta == 0:
                logger.info(f"[QPACK] 使用引用: cookie -> 索引 62 (2 bytes)")
                logger.info(f"[QPACK] 使用引用: x-custom-auth-token -> 索引 63 (2 bytes)")
                logger.info(f"[QPACK] 完全使用动态表引用")
                cookie_saved = len(LARGE_COOKIE) + len("cookie") + 4  # 头部名称 + 值 + 开销
                token_saved = len(LARGE_AUTH_TOKEN) + len("x-custom-auth-token") + 4
                logger.info(f"[压缩统计] 节省: cookie ~{cookie_saved} bytes")
                logger.info(f"[压缩统计] 节省: x-custom-auth-token ~{token_saved} bytes")
            else:
                logger.info(f"[QPACK] 部分使用动态表引用 + 新字面量")

            compression_ratio = (1 - actual_size / estimated_size) * 100 if estimated_size > 0 else 0
            bytes_saved = estimated_size - actual_size
            logger.info(f"[压缩统计] 总节省: {bytes_saved} bytes ({compression_ratio:.1f}%)")

        # 总体统计
        total_compression = self._stats.get_compression_ratio()
        total_saved = self._stats.get_bytes_saved()
        logger.info(f"[总体统计] 请求数: {request_num}")
        logger.info(f"[总体统计] 累计节省: {total_saved} bytes ({total_compression:.1f}%)")

        # 显示动态表效果
        if request_num > 1:
            avg_compression = total_compression
            logger.info(f"[动态表效果] 平均压缩率: {avg_compression:.1f}%")
            if avg_compression > 50:
                logger.info(f"[动态表效果] ✓ QPACK动态表工作良好")
            else:
                logger.info(f"[动态表效果] ⚠ 压缩效果有限")

        logger.info("=" * 60)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HTTP/3 QPACK调试客户端")
    parser.add_argument("--host", default="aliyun.hawks.top", help="服务器主机名")
    parser.add_argument("--port", type=int, default=443, help="服务器端口")
    parser.add_argument("--path", default="/", help="请求路径")
    parser.add_argument("--requests", type=int, default=3, help="发送请求次数")
    parser.add_argument("--insecure", action="store_true", help="跳过证书验证")
    parser.add_argument("--quic-log", help="QUIC日志文件路径夹")
    parser.add_argument("--secrets-log", help="SSL密钥日志文件路径（用于Wireshark解密）")
    
    args = parser.parse_args() 
    
    # 配置QUIC连接
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=H3_ALPN,
        max_datagram_frame_size=65536,
    )
    
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
        
    if args.quic_log:
        # 确保QUIC日志目录存在
        quic_log_path = Path(args.quic_log)
        quic_log_path.mkdir(parents=True, exist_ok=True)
        configuration.quic_logger = QuicFileLogger(args.quic_log)

    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
        
    # 构建URL
    url = f"https://{args.host}:{args.port}{args.path}"
    
    logger.info(f"连接到 {args.host}:{args.port}")
    logger.info(f"将发送 {args.requests} 个请求到 {url}")
    logger.info(f"大型Cookie头部: {len(LARGE_COOKIE)} bytes")
    logger.info(f"大型Auth-Token头部: {len(LARGE_AUTH_TOKEN)} bytes")
    if args.quic_log:
        logger.info(f"QUIC日志将保存到: {args.quic_log}")
    if args.secrets_log:
        logger.info(f"SSL密钥日志将保存到文件夹: {args.secrets_log}")
        logger.info("💡 提示: 在Wireshark中设置 Edit -> Preferences -> Protocols -> TLS -> (Pre)-Master-Secret log filename")
    logger.info("=" * 60)
    
    # 建立连接
    async with connect(
        args.host,
        args.port,
        configuration=configuration,
        create_protocol=QpackDebugClient,
    ) as client:
        client = client  # type: QpackDebugClient
        
        # 发送多个请求来测试QPACK动态表
        for i in range(args.requests):
            # 构建包含大型头部字段的请求
            headers = {
                "cookie": LARGE_COOKIE,
                "x-custom-auth-token": LARGE_AUTH_TOKEN,
                "x-request-id": f"req-{i+1}-{int(time.time())}",
            }
            
            try:
                events = await client.send_request(url, headers)
                
                # 处理响应
                for event in events:
                    if isinstance(event, HeadersReceived):
                        logger.info(f"[响应] 状态码: {dict(event.headers).get(b':status', b'unknown').decode()}")
                        
                # 在请求之间稍作延迟
                if i < args.requests - 1:
                    await asyncio.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"请求 {i+1} 失败: {e}")
                
    logger.info("所有请求完成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断")
    except Exception as e:
        logger.error(f"程序错误: {e}")


#  使用方式
# python qpack_debug_client.py --host aliyun.hawks.top --requests 4 --insecure --quic-log qpack_debug