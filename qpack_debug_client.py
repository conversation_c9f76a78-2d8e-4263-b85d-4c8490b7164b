#!/usr/bin/env python3
"""
HTTP/3客户端，支持QPACK动态表调试功能

这个客户端基于aioquic项目，专门用于测试和调试QPACK动态表的压缩功能。
它能够发送包含大型头部字段的HTTP/3请求，并显示详细的QPACK操作信息。
"""

import argparse
import asyncio
import logging
import ssl
import sys
import time
from collections import deque
from typing import Dict, List, Optional, Deque
from urllib.parse import urlparse
from pathlib import Path

# 使用已安装的aioquic
# sys.path.insert(0, str(Path(__file__).parent / "src"))

import aioquic
from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h3.connection import H3_ALPN, H3Connection
from aioquic.h3.events import DataReceived, H3Event, HeadersReceived
from aioquic.quic.configuration import QuicConfiguration
from aioquic.quic.events import QuicEvent
from aioquic.quic.logger import QuicFileLogger

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("qpack_debug_client")

# 大型头部字段用于测试QPACK动态表
LARGE_COOKIE = "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
LARGE_AUTH_TOKEN = "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"

USER_AGENT = f"qpack-debug-client/aioquic-{aioquic.__version__}"


class QpackDebugStats:
    """QPACK调试统计信息"""
    
    def __init__(self):
        self.request_count = 0
        self.total_header_bytes_before = 0
        self.total_header_bytes_after = 0
        self.dynamic_table_entries = []
        
    def add_request(self, headers_before: int, headers_after: int):
        """统计QPACK压缩效果
        
        headers_before: 压缩前的头部字节数
        headers_after: 压缩后的头部字节数
        """
        self.request_count += 1
        self.total_header_bytes_before += headers_before
        self.total_header_bytes_after += headers_after
        
    def get_compression_ratio(self) -> float:
        if self.total_header_bytes_before == 0:
            return 0.0
        return (1 - self.total_header_bytes_after / self.total_header_bytes_before) * 100
        
    def get_bytes_saved(self) -> int:
        return self.total_header_bytes_before - self.total_header_bytes_after


class QpackDebugClient(QuicConnectionProtocol):
    """支持QPACK调试的HTTP/3客户端"""

    def __init__(self, *args, **kwargs):

        super().__init__(*args, **kwargs)
        # HTTP/3连接对象
        self._http: Optional[H3Connection] = None
        # 保存每个流的HTTP/3事件
        self._request_events: Dict[int, Deque[H3Event]] = {}
        # 保存每个流的事件等待器
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        # QPACK调试统计信息
        self._stats = QpackDebugStats()

        # 初始化HTTP/3连接
        if self._quic.configuration.alpn_protocols[0] in H3_ALPN:
            self._http = H3Connection(self._quic)

    def quic_event_received(self, event: QuicEvent) -> None:
        """处理QUIC事件"""
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)

    def http_event_received(self, event: H3Event) -> None:
        """处理HTTP/3事件"""
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[stream_id].append(event)

                # 如果流结束，完成Future
                if event.stream_ended and stream_id in self._request_waiter:
                    waiter = self._request_waiter.pop(stream_id)
                    waiter.set_result(self._request_events.pop(stream_id))
                    
    async def send_request(self, url: str, headers: Optional[Dict[str, str]] = None) -> Deque[H3Event]:
        """发送HTTP/3请求"""
        if self._http is None:
            raise RuntimeError("HTTP/3连接未初始化")

        # 解析URL
        parsed = urlparse(url)
        authority = parsed.netloc
        path = parsed.path or "/"
        if parsed.query:
            path += "?" + parsed.query

        # 构建请求头部
        request_headers = [
            (b":method", b"GET"),
            (b":scheme", b"https"),
            (b":authority", authority.encode()),
            (b":path", path.encode()),
            (b"user-agent", USER_AGENT.encode()),
        ]

        # 添加自定义头部
        if headers:
            for name, value in headers.items():
                request_headers.append((name.encode(), value.encode()))

        # 记录请求前的QPACK状态
        encoder_bytes_before = self._http._encoder_bytes_sent
        decoder_bytes_before = self._http._decoder_bytes_sent

        # 估算头部大小（用于统计）,+4 处理长度前缀
        header_size_estimate = sum(len(name) + len(value) + 4 for name, value in request_headers)

        logger.info(f"[请求 {self._stats.request_count + 1}] 发送请求到 {url}")
        logger.info(f"[QPACK] 预估头部大小: {header_size_estimate} bytes")

        # 发送请求 - 使用自定义方法来捕获真实的QPACK数据
        stream_id = self._quic.get_next_available_stream_id()
        encoder_data, frame_data = self._encode_headers_with_debug(stream_id, request_headers)

        # 发送编码后的头部
        from aioquic.h3.connection import FrameType, encode_frame
        self._quic.send_stream_data(
            stream_id, encode_frame(FrameType.HEADERS, frame_data), True
        )

        # 设置等待器
        waiter = self._loop.create_future()
        self._request_waiter[stream_id] = waiter
        self._request_events[stream_id] = deque()

        # 传输数据
        self.transmit()

        # 等待响应
        events = await asyncio.shield(waiter)

        # 记录请求后的QPACK状态
        encoder_bytes_after = self._http._encoder_bytes_sent
        decoder_bytes_after = self._http._decoder_bytes_sent

        # 计算实际传输的头部字节数
        actual_header_bytes = len(frame_data)

        # 更新统计信息
        self._stats.add_request(header_size_estimate, actual_header_bytes)

        # 输出QPACK调试信息 - 传递真实的编码器数据
        self._print_qpack_debug_info(
            request_headers,
            header_size_estimate,
            actual_header_bytes,
            encoder_data,
            frame_data,
            encoder_bytes_after - encoder_bytes_before,
            decoder_bytes_after - decoder_bytes_before
        )

        return events

    def _encode_headers_with_debug(self, stream_id: int, headers) -> tuple:
        """编码头部并返回调试信息"""
        # 直接调用编码器获取真实数据
        encoder_data, frame_data = self._http._encoder.encode(stream_id, headers)

        # 发送编码器数据到编码器流
        if encoder_data:
            self._http._encoder_bytes_sent += len(encoder_data)
            self._quic.send_stream_data(self._http._local_encoder_stream_id, encoder_data)

        return encoder_data, frame_data

    def _print_qpack_debug_info(self, headers, estimated_size: int, actual_size: int,
                               encoder_data: bytes, frame_data: bytes,
                               encoder_delta: int, decoder_delta: int):
        """打印真实的QPACK调试信息"""
        request_num = self._stats.request_count

        logger.info(f"[QPACK 真实数据] 编码器字节增量: {encoder_delta}")
        logger.info(f"[QPACK 真实数据] 解码器字节增量: {decoder_delta}")
        logger.info(f"[QPACK 真实数据] 编码器累计字节: {self._http._encoder_bytes_sent}")
        logger.info(f"[QPACK 真实数据] 解码器累计字节: {self._http._decoder_bytes_sent}")

        # 显示真实的编码器数据
        if encoder_data:
            logger.info(f"[QPACK 编码器流] 数据长度: {len(encoder_data)} bytes")
            logger.info(f"[QPACK 编码器流] 十六进制数据: {encoder_data.hex()}")
            self._analyze_encoder_data(encoder_data)
        else:
            logger.info(f"[QPACK 编码器流] 无编码器数据 - 可能使用静态表或已有动态表条目")

        # 显示真实的帧数据
        logger.info(f"[QPACK 头部帧] 压缩后大小: {len(frame_data)} bytes")
        logger.info(f"[QPACK 头部帧] 十六进制数据: {frame_data.hex()}")

        # 分析头部帧数据
        self._analyze_frame_data(frame_data, headers)

        # 计算真实的压缩比例
        if estimated_size > 0:
            compression_ratio = (1 - actual_size / estimated_size) * 100
            bytes_saved = estimated_size - actual_size
            logger.info(f"[真实压缩统计] 原始大小: {estimated_size} bytes")
            logger.info(f"[真实压缩统计] 压缩后大小: {actual_size} bytes")
            logger.info(f"[真实压缩统计] 节省: {bytes_saved} bytes ({compression_ratio:.1f}%)")

        # 总体统计
        total_compression = self._stats.get_compression_ratio()
        total_saved = self._stats.get_bytes_saved()
        logger.info(f"[总体统计] 请求数: {request_num}")
        logger.info(f"[总体统计] 累计节省: {total_saved} bytes ({total_compression:.1f}%)")

        # 动态表效果分析
        if request_num > 1:
            if encoder_delta == 0:
                logger.info(f"[动态表效果] ✓ 完全使用动态表引用，无需发送新条目")
                logger.info(f"[动态表效果] 这表明之前建立的动态表条目被有效重用")
            else:
                logger.info(f"[动态表效果] ⚠ 仍在发送编码器数据，可能添加新条目")
                logger.info(f"[动态表效果] 编码器正在优化动态表以提高后续压缩效果")

        logger.info("=" * 60)

    def _analyze_encoder_data(self, encoder_data: bytes):
        """分析编码器流数据"""
        if not encoder_data:
            return

        logger.info(f"[编码器数据分析] 正在分析 {len(encoder_data)} 字节的编码器数据...")

        # 简单的QPACK编码器指令分析
        # 根据QPACK规范，编码器指令的第一个字节指示指令类型
        i = 0
        instruction_count = 0

        while i < len(encoder_data):
            instruction_count += 1
            byte = encoder_data[i]

            if byte & 0x80:  # 10xxxxxx - Insert With Name Reference
                logger.info(f"[编码器指令 {instruction_count}] 插入带名称引用 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 向动态表添加新条目，引用现有名称")
                # 跳过这个指令的其余部分（简化处理）
                i += 1
                while i < len(encoder_data) and encoder_data[i] & 0x80 == 0:
                    i += 1

            elif byte & 0x40:  # 01xxxxxx - Insert With Literal Name
                logger.info(f"[编码器指令 {instruction_count}] 插入带字面量名称 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 向动态表添加全新的头部字段")
                i += 1

            elif byte & 0x20:  # 001xxxxx - Duplicate
                index = byte & 0x1f
                logger.info(f"[编码器指令 {instruction_count}] 复制条目 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 复制动态表索引 {index} 的条目")
                i += 1

            else:  # 00xxxxxx - Set Dynamic Table Capacity
                capacity = byte & 0x3f
                logger.info(f"[编码器指令 {instruction_count}] 设置动态表容量 (0x{byte:02x})")
                logger.info(f"[编码器指令 {instruction_count}] → 设置容量为 {capacity}")
                i += 1

        logger.info(f"[编码器数据分析] 共发现 {instruction_count} 个编码器指令")
        if instruction_count > 0:
            logger.info(f"[编码器数据分析] ✓ 检测到动态表操作！")
            logger.info(f"[QPACK机制说明] 编码器将重复数据分解为多个动态表条目")
            logger.info(f"[QPACK机制说明] 这样可以在后续请求中通过索引引用实现高压缩率")

    def _analyze_frame_data(self, frame_data: bytes, headers):
        """分析头部帧数据"""
        if not frame_data:
            return

        logger.info(f"[头部帧分析] 正在分析 {len(frame_data)} 字节的头部数据...")

        # 分析QPACK头部块
        # 前两个字节通常是Required Insert Count和Base
        if len(frame_data) >= 2:
            required_insert_count = frame_data[0]
            base = frame_data[1] if len(frame_data) > 1 else 0
            logger.info(f"[头部帧分析] Required Insert Count: {required_insert_count}")
            logger.info(f"[头部帧分析] Base: {base}")

            if required_insert_count > 0:
                logger.info(f"[头部帧分析] ✓ 依赖动态表条目 (需要 {required_insert_count} 个插入)")
            else:
                logger.info(f"[头部帧分析] → 不依赖动态表条目")

        # 分析头部字段表示
        i = 2  # 跳过前缀
        field_count = 0

        while i < len(frame_data):
            field_count += 1
            byte = frame_data[i]

            if byte & 0x80:  # 1xxxxxxx - Indexed Header Field
                index = byte & 0x7f
                logger.info(f"[头部字段 {field_count}] 索引头部字段 (0x{byte:02x})")
                logger.info(f"[头部字段 {field_count}] → 使用索引 {index} (动态表引用!)")
                i += 1

            elif byte & 0x40:  # 01xxxxxx - Literal Header Field with Name Reference
                index = byte & 0x3f
                logger.info(f"[头部字段 {field_count}] 字面量头部字段带名称引用 (0x{byte:02x})")
                logger.info(f"[头部字段 {field_count}] → 引用索引 {index} 的名称")
                i += 1
                # 跳过值部分（简化）
                while i < len(frame_data) and frame_data[i] & 0x80 == 0:
                    i += 1

            elif byte & 0x20:  # 001xxxxx - Literal Header Field with Literal Name
                logger.info(f"[头部字段 {field_count}] 字面量头部字段带字面量名称 (0x{byte:02x})")
                logger.info(f"[头部字段 {field_count}] → 完全字面量字段")
                i += 1

            else:  # 其他模式
                logger.info(f"[头部字段 {field_count}] 其他编码模式 (0x{byte:02x})")
                i += 1

        logger.info(f"[头部帧分析] 共发现 {field_count} 个头部字段编码")

        # 检查是否有动态表使用的证据
        dynamic_table_used = any(b & 0x80 and (b & 0x7f) >= 62 for b in frame_data[2:])
        if dynamic_table_used:
            logger.info(f"[头部帧分析] ✓ 检测到动态表索引使用！")
        else:
            logger.info(f"[头部帧分析] → 未检测到明显的动态表使用")


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="HTTP/3 QPACK调试客户端")
    parser.add_argument("--host", default="aliyun.hawks.top", help="服务器主机名")
    parser.add_argument("--port", type=int, default=443, help="服务器端口")
    parser.add_argument("--path", default="/", help="请求路径")
    parser.add_argument("--requests", type=int, default=3, help="发送请求次数")
    parser.add_argument("--insecure", action="store_true", help="跳过证书验证")
    parser.add_argument("--quic-log", help="QUIC日志文件路径夹")
    parser.add_argument("--secrets-log", help="SSL密钥日志文件路径（用于Wireshark解密）")
    
    args = parser.parse_args() 
    
    # 配置QUIC连接
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=H3_ALPN,
        max_datagram_frame_size=65536,
    )
    
    if args.insecure:
        configuration.verify_mode = ssl.CERT_NONE
        
    if args.quic_log:
        # 确保QUIC日志目录存在
        quic_log_path = Path(args.quic_log)
        quic_log_path.mkdir(parents=True, exist_ok=True)
        configuration.quic_logger = QuicFileLogger(args.quic_log)

    if args.secrets_log:
        configuration.secrets_log_file = open(args.secrets_log, "a")
        
    # 构建URL
    url = f"https://{args.host}:{args.port}{args.path}"
    
    logger.info(f"连接到 {args.host}:{args.port}")
    logger.info(f"将发送 {args.requests} 个请求到 {url}")
    logger.info(f"大型Cookie头部: {len(LARGE_COOKIE)} bytes")
    logger.info(f"大型Auth-Token头部: {len(LARGE_AUTH_TOKEN)} bytes")
    if args.quic_log:
        logger.info(f"QUIC日志将保存到: {args.quic_log}")
    if args.secrets_log:
        logger.info(f"SSL密钥日志将保存到文件夹: {args.secrets_log}")
        logger.info("💡 提示: 在Wireshark中设置 Edit -> Preferences -> Protocols -> TLS -> (Pre)-Master-Secret log filename")
    logger.info("=" * 60)
    
    # 建立连接
    async with connect(
        args.host,
        args.port,
        configuration=configuration,
        create_protocol=QpackDebugClient,
    ) as client:
        client = client  # type: QpackDebugClient
        
        # 发送多个请求来测试QPACK动态表
        for i in range(args.requests):
            # 构建包含大型头部字段的请求
            headers = {
                "cookie": LARGE_COOKIE,
                "x-custom-auth-token": LARGE_AUTH_TOKEN,
                "x-request-id": f"req-{i+1}-{int(time.time())}",
            }
            
            try:
                events = await client.send_request(url, headers)
                
                # 处理响应
                for event in events:
                    if isinstance(event, HeadersReceived):
                        logger.info(f"[响应] 状态码: {dict(event.headers).get(b':status', b'unknown').decode()}")
                        
                # 在请求之间稍作延迟
                if i < args.requests - 1:
                    await asyncio.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"请求 {i+1} 失败: {e}")
                
    logger.info("所有请求完成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断")
    except Exception as e:
        logger.error(f"程序错误: {e}")


#  使用方式
# python qpack_debug_client.py --host aliyun.hawks.top --requests 4 --insecure --quic-log qpack_debug