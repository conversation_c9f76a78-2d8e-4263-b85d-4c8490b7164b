#!/usr/bin/env python3
"""
QPACK动态表演示客户端

这是一个简化版的HTTP/3客户端，专门用于演示QPACK动态表的核心功能。
它展示了如何在HTTP/3中使用大型头部字段，以及QPACK如何通过动态表实现压缩。
"""

import asyncio
import logging
import ssl
from collections import deque
from typing import Dict, Optional, Deque

from aioquic.asyncio.client import connect
from aioquic.asyncio.protocol import QuicConnectionProtocol
from aioquic.h3.connection import H3_ALPN, H3Connection
from aioquic.h3.events import DataReceived, H3Event, HeadersReceived
from aioquic.quic.configuration import QuicConfiguration

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger("qpack_demo")

# 大型头部字段用于演示QPACK动态表
DEMO_COOKIE = "a" * 100  # 100字节的Cookie
DEMO_TOKEN = "e" * 50    # 50字节的认证令牌

class QpackDemo(QuicConnectionProtocol):
    """QPACK动态表演示客户端"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._http: Optional[H3Connection] = None
        self._request_events: Dict[int, Deque[H3Event]] = {}
        self._request_waiter: Dict[int, asyncio.Future[Deque[H3Event]]] = {}
        self._request_count = 0
        
        # 初始化HTTP/3连接
        if self._quic.configuration.alpn_protocols[0] in H3_ALPN:
            self._http = H3Connection(self._quic)
            
    def quic_event_received(self, event) -> None:
        """处理QUIC事件"""
        if self._http is not None:
            for http_event in self._http.handle_event(event):
                self.http_event_received(http_event)
                
    def http_event_received(self, event: H3Event) -> None:
        """处理HTTP/3事件"""
        if isinstance(event, (HeadersReceived, DataReceived)):
            stream_id = event.stream_id
            if stream_id in self._request_events:
                self._request_events[stream_id].append(event)
                
                # 如果流结束，完成Future
                if event.stream_ended and stream_id in self._request_waiter:
                    waiter = self._request_waiter.pop(stream_id)
                    waiter.set_result(self._request_events.pop(stream_id))
                    
    async def send_demo_request(self, url: str) -> None:
        """发送演示请求"""
        if self._http is None:
            raise RuntimeError("HTTP/3连接未初始化")
            
        self._request_count += 1
        
        # 构建包含大型头部字段的请求
        headers = [
            (b":method", b"GET"),
            (b":scheme", b"https"),
            (b":authority", b"aliyun.hawks.top"),
            (b":path", b"/"),
            (b"user-agent", b"qpack-demo-client"),
            (b"cookie", DEMO_COOKIE.encode()),
            (b"x-auth-token", DEMO_TOKEN.encode()),
        ]
        
        # 记录QPACK状态
        encoder_before = self._http._encoder_bytes_sent
        decoder_before = self._http._decoder_bytes_sent
        
        # 计算头部大小
        header_size = sum(len(name) + len(value) + 4 for name, value in headers)
        
        print(f"\n🚀 请求 {self._request_count}")
        print(f"📊 头部总大小: {header_size} bytes")
        print(f"🍪 Cookie大小: {len(DEMO_COOKIE)} bytes")
        print(f"🔑 Token大小: {len(DEMO_TOKEN)} bytes")
        
        # 发送请求
        stream_id = self._quic.get_next_available_stream_id()
        self._http.send_headers(stream_id=stream_id, headers=headers, end_stream=True)
        
        # 等待响应
        waiter = self._loop.create_future()
        self._request_waiter[stream_id] = waiter
        self._request_events[stream_id] = deque()
        self.transmit()
        
        events = await asyncio.shield(waiter)
        
        # 分析QPACK效果
        encoder_after = self._http._encoder_bytes_sent
        decoder_after = self._http._decoder_bytes_sent
        
        encoder_delta = encoder_after - encoder_before
        decoder_delta = decoder_after - decoder_before
        
        print(f"📈 QPACK编码器增量: {encoder_delta} bytes")
        print(f"📉 QPACK解码器增量: {decoder_delta} bytes")
        
        if self._request_count == 1:
            print("🏗️  第一次请求 - 建立动态表")
            if encoder_delta > 0:
                print("➕ 大型头部字段被添加到动态表")
                print(f"   Cookie -> 动态表索引 62")
                print(f"   Auth-Token -> 动态表索引 63")
            else:
                print("ℹ️  头部可能已在静态表中")
        else:
            if encoder_delta == 0:
                print("✨ 完全使用动态表引用!")
                print(f"   Cookie: 索引引用 (节省 ~{len(DEMO_COOKIE)} bytes)")
                print(f"   Token: 索引引用 (节省 ~{len(DEMO_TOKEN)} bytes)")
                saved = len(DEMO_COOKIE) + len(DEMO_TOKEN) + 20  # 估算节省
                print(f"💰 总节省: ~{saved} bytes ({saved/header_size*100:.1f}%)")
            else:
                print("🔄 部分使用动态表引用")
        
        # 显示响应状态
        for event in events:
            if isinstance(event, HeadersReceived):
                status = dict(event.headers).get(b':status', b'unknown').decode()
                print(f"✅ 响应状态: {status}")
                break
                
        print("─" * 50)


async def main():
    """主演示函数"""
    import sys

    # 检查命令行参数
    secrets_log = None
    if len(sys.argv) > 1 and sys.argv[1] == "--secrets-log":
        if len(sys.argv) > 2:
            secrets_log = sys.argv[2]
        else:
            secrets_log = "qpack_demo_secrets.log"

    print("🌟 QPACK动态表演示")
    print("=" * 50)
    print("这个演示将展示HTTP/3中QPACK动态表的工作原理")
    print(f"我们将发送包含大型头部字段的请求到 aliyun.hawks.top")
    print(f"观察QPACK如何在后续请求中压缩这些头部字段")
    if secrets_log:
        print(f"🔑 SSL密钥日志: {secrets_log}")
        print("💡 在Wireshark中设置TLS密钥日志文件来解密流量")
    print("=" * 50)

    # 配置QUIC连接
    configuration = QuicConfiguration(
        is_client=True,
        alpn_protocols=H3_ALPN,
        verify_mode=ssl.CERT_NONE,  # 跳过证书验证用于演示
    )

    # 添加SSL密钥日志
    if secrets_log:
        configuration.secrets_log_file = open(secrets_log, "a")
    
    # 建立连接并发送演示请求
    async with connect(
        "aliyun.hawks.top",
        443,
        configuration=configuration,
        create_protocol=QpackDemo,
    ) as client:
        
        print("🔗 正在连接到服务器...")
        
        # 发送多个请求来演示QPACK动态表效果
        for i in range(4):
            await client.send_demo_request("https://aliyun.hawks.top/")
            if i < 3:  # 在请求之间稍作延迟
                await asyncio.sleep(0.5)
    
    print("\n🎉 演示完成!")
    print("\n📝 总结:")
    print("1. 第一次请求建立了动态表条目")
    print("2. 后续请求使用动态表引用，显著减少传输字节数")
    print("3. QPACK动态表有效压缩了重复的大型头部字段")
    print("4. 这种压缩机制在实际应用中可以大幅提升性能")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示出错: {e}")
